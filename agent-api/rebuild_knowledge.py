#!/usr/bin/env python3
"""
Unified rebuild script:
1) Run Google docs collection: python -m google_api.run (cwd=agent-api/agent-api)
2) On success, load/reindex knowledge base into vector DB using get_avangard_knowledge().load(recreate=True)

Exit codes:
 0 - success
 1 - google collection failed
 2 - knowledge load failed
"""
import subprocess
import sys
import os
import traceback

# Resolve paths
THIS_DIR = os.path.dirname(os.path.abspath(__file__))           # .../agent-api/agent-api
BACKEND_ROOT = THIS_DIR                                         # keep reference
GOOGLE_MODULE_CWD = os.path.join(BACKEND_ROOT)                  # run inside agent-api/agent-api
GOOGLE_RUN_SCRIPT = os.path.join(BACKEND_ROOT, 'google_api', 'run')


def run_google_collection() -> None:
    print(f"[1/2] 🚀 Running Google docs collection: {GOOGLE_RUN_SCRIPT}", flush=True)
    try:
        # Prefer executing the run script directly via bash to handle non-.py runner
        cmd = ["/bin/bash", GOOGLE_RUN_SCRIPT] if os.path.isfile(GOOGLE_RUN_SCRIPT) else [sys.executable, "-m", "google_api.run"]
        proc = subprocess.run(cmd, cwd=GOOGLE_MODULE_CWD, stdout=subprocess.PIPE, stderr=subprocess.STDOUT, text=True, check=False)
        print(proc.stdout)
        if proc.returncode != 0:
            print(f"❌ google_api.run failed with code {proc.returncode}")
            sys.exit(1)
        print("✅ Google docs collection finished successfully")
    except Exception:
        traceback.print_exc()
        sys.exit(1)


def load_knowledge() -> None:
    print("[2/2] 📚 Loading knowledge into vector DB (recreate=True)", flush=True)
    try:
        # Import after collection to ensure environment is ready
        from agents.avangard_consultant import get_avangard_knowledge
        kb = get_avangard_knowledge()
        # Force (re)load index
        kb.load(recreate=True)
        print("✅ Knowledge loaded successfully")
    except Exception:
        traceback.print_exc()
        sys.exit(2)


def main():
    run_google_collection()
    load_knowledge()
    print("🎉 Rebuild completed")


if __name__ == "__main__":
    main()
