import os
from dotenv import load_dotenv
from agno.agent import Agent
from agno.tools.googlesheets import GoogleSheetsTools

# Загружаем переменные окружения
load_dotenv()

# Используем ваши данные из .env
SPREADSHEET_ID = os.getenv("GOOGLE_SPREADSHEET_ID", "1Mqak3XJtkwC-UJ1ZGihGNm28trUwpVQfX3EKpY9iMHo")
# Убираем фиксированный диапазон, чтобы агент мог работать с любыми листами
SAMPLE_RANGE_NAME = None  # Позволяем агенту самому выбирать диапазоны

print(f"🔍 Тестируем Google Sheets с множественными листами:")
print(f"📊 Spreadsheet ID: {SPREADSHEET_ID}")
print(f"📋 Range: Динамический (агент выбирает сам)")
print(f"🔑 Credentials: {os.getenv('GOOGLE_CREDENTIALS_PATH')}")

# Устанавливаем переменную окружения для Google credentials
os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = os.getenv('GOOGLE_CREDENTIALS_PATH', 'meta-geography-464110-b9-55c203771d7e.json')

google_sheets_tools = GoogleSheetsTools(
    spreadsheet_id=SPREADSHEET_ID,
    # НЕ указываем spreadsheet_range - пусть агент использует диапазоны из запросов
    read=True,      # Чтение данных
    update=True,    # Обновление данных
    create=True,    # Создание новых листов
    duplicate=True  # Дублирование листов
)

try:
    print("🤖 Создаем агента...")
    agent = Agent(
        tools=[google_sheets_tools],
        instructions=[
            "You help users interact with Google Sheets using tools that use the Google Sheets API",
            "You can work with multiple sheets in a spreadsheet",
            "When reading data, you can specify sheet names like 'Sheet1!A1:E10' or just 'A1:E10' for the first sheet",
            "You can create new sheets, duplicate existing ones, and work with any sheet in the spreadsheet",
        ],
    )
    
    # Закомментированные тесты
    # print("📋 Тестируем чтение разных листов...")
    # agent.print_response("Read data from 'Sheet1!A1:F10' to see the first sheet")
    # 
    # print("\n" + "="*50)
    # agent.print_response("Read data from 'Sheet2!A1:F10' to see the second sheet")
    # 
    # print("\n" + "="*50)
    # agent.print_response("Read data from 'Лист1!A1:F10' if there's a Russian named sheet")
    # 
    # print("\n" + "="*60)
    # print("📊 Тестируем чтение первого листа...")
    # agent.print_response("Read data from the first sheet, show me first 10 rows")
    # 
    # print("\n" + "="*60)
    # print("📊 Тестируем чтение конкретного листа...")
    # agent.print_response("Read data from sheet named 'Sheet1' or the main sheet, range A1:F15")
    # 
    # print("\n" + "="*60)
    # print("🔍 Тестируем поиск товара на всех листах...")
    # agent.print_response("Найди канатоукладчик для тали 2,0 тн на любом листе таблицы и покажи его цену и количество")
    # 
    # print("\n" + "="*60)
    # print("📝 Тестируем создание нового листа...")
    # agent.print_response("Create a new sheet called 'Test_Sheet' in this spreadsheet")
    # 
    # print("\n" + "="*60)
    # print("✏️ Тестируем запись в новый лист...")
    # agent.print_response("Write 'Тест записи' to cell A1 in the 'Test_Sheet' sheet")
    # 
    # print("\n" + "="*60)
    # print("📝 Тестируем создание листа с явным ID...")
    # agent.print_response(f"Create a new sheet called 'Test_Sheet' in spreadsheet {SPREADSHEET_ID}")
    # 
    # print("\n" + "="*60)
    # print("✏️ Тестируем запись в конкретный лист...")
    # agent.print_response(f"Write 'Тест записи' to cell A1 in the 'Test_Sheet' sheet of spreadsheet {SPREADSHEET_ID}")
    
    # Закомментированные тесты
    # print("📋 Тестируем максимальное количество строк (100)...")
    # agent.print_response(f"Read first 100 rows from sheet 'Запчасти к талям россия' range A1:F100 in spreadsheet {SPREADSHEET_ID}")
    # 
    # print("\n" + "="*60)
    # print("🔍 Тестируем поиск в больших данных...")
    # agent.print_response(f"Find all 'электромагнит' items in sheet 'Запчасти к талям россия' range A1:F100 in spreadsheet {SPREADSHEET_ID}")
    # 
    # print("\n" + "="*60)
    # print("📊 Тестируем подсчет товаров...")
    # agent.print_response(f"Count how many different types of products are in sheet 'Запчасти к талям россия' range A1:F100 in spreadsheet {SPREADSHEET_ID}")
    
    # Тест поиска и редактирования конкретного товара
    print("🔍 Ищем товар для редактирования...")
    agent.print_response(f"Find 'Вал редуктора к тали г/п 1,0 тн-12м' in sheet 'Запчасти Болгария' and show its current quantity in spreadsheet {SPREADSHEET_ID}")
    
    print("\n" + "="*60)
    print("✏️ Изменяем количество товара на 42...")
    agent.print_response(f"Update quantity of 'Вал редуктора к тали г/п 1,0 тн-12м' to 42 in sheet 'Запчасти Болгария' in spreadsheet {SPREADSHEET_ID}")
    
    print("\n" + "="*60)
    print("🔍 Проверяем изменение...")
    agent.print_response(f"Find 'Вал редуктора к тали г/п 1,0 тн-12м' in sheet 'Запчасти Болгария' and show its current quantity in spreadsheet {SPREADSHEET_ID}")
    
except Exception as e:
    print(f"❌ Ошибка: {e}")
    import traceback
    traceback.print_exc()
