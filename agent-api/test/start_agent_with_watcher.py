#!/usr/bin/env python3
"""
Запуск агента Avangard с автоматическим мониторингом файлов.
"""

import asyncio
import uvicorn
import threading
from knowledge_watcher import KnowledgeWatcher
from agents.avangard_consultant import get_avangard_consultant

# Глобальные переменные
current_agent = None
watcher_instance = None

async def reload_agent_knowledge(changed_files=None):
    """Перезагружает базу знаний агента инкрементально."""
    global current_agent
    
    if current_agent is None:
        print("⚠️ Агент не инициализирован")
        return
    
    try:
        if changed_files:
            print(f"🔄 Обновление базы знаний для файлов: {[f.name for f in changed_files]}")
        else:
            print("🔄 Инкрементальное обновление базы знаний...")
        
        # Сначала пробуем инкрементальное обновление
        try:
            current_agent.knowledge.load(recreate=False)
            print("✅ База знаний обновлена инкрементально")
        except Exception as e:
            print(f"⚠️ Инкрементальное обновление не удалось: {e}")
            print("🔄 Выполняем полную перезагрузку...")
            current_agent.knowledge.load(recreate=True)
            print("✅ База знаний полностью перезагружена")
        
    except Exception as e:
        print(f"❌ Ошибка при перезагрузке базы знаний: {e}")

def start_file_watcher():
    """Запускает file watcher в отдельном потоке."""
    global watcher_instance
    
    async def run_watcher():
        watcher_instance = KnowledgeWatcher(
            reload_callback=reload_agent_knowledge,
            debounce_seconds=3.0  # Ждем 3 секунды перед перезагрузкой
        )
        
        print("🔍 Запуск file watcher для автоматического обновления базы знаний...")
        try:
            await watcher_instance.start_watching()
        except Exception as e:
            print(f"❌ Ошибка в file watcher: {e}")
    
    # Запускаем watcher в новом event loop
    def run_in_thread():
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            loop.run_until_complete(run_watcher())
        except KeyboardInterrupt:
            print("🛑 File watcher остановлен")
        finally:
            loop.close()
    
    watcher_thread = threading.Thread(target=run_in_thread, daemon=True)
    watcher_thread.start()
    print("✅ File watcher запущен в фоновом режиме")
    return watcher_thread

def main():
    """Основная функция."""
    global current_agent
    
    print("=" * 60)
    print("🚀 Агент Avangard с автоматическим мониторингом файлов")
    print("=" * 60)
    
    # Инициализируем агента
    print("🔧 Инициализация агента...")
    current_agent = get_avangard_consultant(debug_mode=True)
    print("✅ Агент инициализирован")
    
    # Запускаем file watcher
    start_file_watcher()
    
    print("\n💡 Теперь можно:")
    print("  1. Запустить uvicorn сервер: uvicorn api.main:app --reload --host 0.0.0.0 --port 8000")
    print("  2. Добавлять файлы в agent_docs_pdf/ или agent_docs_other/")
    print("  3. База знаний будет автоматически обновляться")
    print("\n⌨️  Нажмите Ctrl+C для остановки")
    
    try:
        # Держим основной поток живым
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Остановка агента и file watcher...")

if __name__ == "__main__":
    import time
    main()