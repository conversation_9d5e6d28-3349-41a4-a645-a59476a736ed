#!/usr/bin/env python3
"""
Безопасный file watcher для автоматического обновления базы знаний агента Avangard.
Использует watchfiles (уже установлен в проекте).
"""

import asyncio
import os
import time
from pathlib import Path
from typing import Optional, Callable
from watchfiles import awatch, Change
import logging

# Настройка логирования
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KnowledgeWatcher:
    """Безопасный watcher для мониторинга папок базы знаний."""
    
    def __init__(self, 
                 pdf_folder: str = "agent_docs_pdf",
                 other_folder: str = "agent_docs_other",
                 reload_callback: Optional[Callable] = None,
                 debounce_seconds: float = 2.0):
        """
        Инициализация watcher'а.
        
        Args:
            pdf_folder: Папка с PDF файлами
            other_folder: Папка с другими документами
            reload_callback: Функция для перезагрузки базы знаний
            debounce_seconds: Задержка перед перезагрузкой (избегаем частых обновлений)
        """
        self.pdf_folder = Path(pdf_folder)
        self.other_folder = Path(other_folder)
        self.reload_callback = reload_callback
        self.debounce_seconds = debounce_seconds
        self.last_reload_time = 0
        self.is_running = False
        
        # Создаем папки если их нет
        self.pdf_folder.mkdir(exist_ok=True)
        self.other_folder.mkdir(exist_ok=True)
        
        # Преобразуем в абсолютные пути для корректного сравнения
        self.pdf_folder = self.pdf_folder.resolve()
        self.other_folder = self.other_folder.resolve()
        
        logger.info(f"📁 Мониторинг папок: {self.pdf_folder}, {self.other_folder}")

    def should_process_file(self, file_path: Path) -> bool:
        """Проверяет, нужно ли обрабатывать файл."""
        # Игнорируем временные файлы и скрытые файлы
        if file_path.name.startswith('.') or file_path.name.startswith('~'):
            logger.debug(f"⏭️ Пропускаем временный/скрытый файл: {file_path.name}")
            return False
        
        # Игнорируем файлы без расширения
        if not file_path.suffix:
            logger.debug(f"⏭️ Пропускаем файл без расширения: {file_path.name}")
            return False
            
        # Поддерживаемые расширения
        supported_extensions = {'.pdf', '.docx', '.csv', '.txt', '.doc', '.rtf'}
        is_supported = file_path.suffix.lower() in supported_extensions
        
        logger.debug(f"📋 Проверка файла: {file_path.name}, расширение: {file_path.suffix}, поддерживается: {is_supported}")
        
        return is_supported

    async def handle_file_change(self, changes):
        """Обрабатывает изменения файлов."""
        logger.info(f"🔍 Обрабатываем {len(changes)} изменений...")
        
        relevant_changes = []
        
        for change_type, file_path in changes:
            file_path = Path(file_path)
            
            # Логируем все изменения для отладки
            change_name = {
                Change.added: "добавлен",
                Change.modified: "изменен", 
                Change.deleted: "удален"
            }.get(change_type, "изменен")
            
            logger.info(f"🔍 Обнаружено изменение: {change_name} - {file_path}")
            
            # Проверяем, что файл в наших папках
            is_in_pdf_folder = file_path.parent == self.pdf_folder
            is_in_other_folder = file_path.parent == self.other_folder
            is_in_folders = is_in_pdf_folder or is_in_other_folder
            
            logger.debug(f"🔍 Проверка папок для {file_path}:")
            logger.debug(f"  - Родительская папка: {file_path.parent}")
            logger.debug(f"  - PDF папка: {self.pdf_folder} (совпадает: {is_in_pdf_folder})")
            logger.debug(f"  - Other папка: {self.other_folder} (совпадает: {is_in_other_folder})")
            logger.debug(f"  - В отслеживаемых папках: {is_in_folders}")
            
            if not is_in_folders:
                logger.debug(f"⏭️ Пропускаем файл (не в отслеживаемых папках): {file_path}")
                continue
            
            # Проверяем, что файл нужно обрабатывать
            if not self.should_process_file(file_path):
                logger.debug(f"⏭️ Пропускаем файл (неподдерживаемый тип): {file_path}")
                continue
                
            relevant_changes.append((change_type, file_path))
            logger.info(f"📄 Релевантное изменение: {change_name} - {file_path}")
        
        # Если есть релевантные изменения, планируем перезагрузку
        if relevant_changes:
            logger.info(f"✅ Найдено {len(relevant_changes)} релевантных изменений")
            changed_files = [file_path for _, file_path in relevant_changes]
            await self.schedule_reload(changed_files)
        else:
            logger.info("ℹ️ Релевантных изменений не найдено")

    async def schedule_reload(self, changed_files=None):
        """Планирует перезагрузку с debounce."""
        current_time = time.time()
        
        # Debounce: избегаем частых перезагрузок
        if current_time - self.last_reload_time < self.debounce_seconds:
            logger.debug(f"⏳ Debounce: ждем {self.debounce_seconds}s перед перезагрузкой")
            return
        
        self.last_reload_time = current_time
        
        # Ждем немного, чтобы файл полностью записался
        await asyncio.sleep(0.5)
        
        if self.reload_callback:
            try:
                logger.info("🔄 Перезагружаем базу знаний...")
                # Передаем информацию о измененных файлах
                if changed_files:
                    await self.reload_callback(changed_files)
                else:
                    await self.reload_callback()
                logger.info("✅ База знаний обновлена")
            except Exception as e:
                logger.error(f"❌ Ошибка при перезагрузке базы знаний: {e}")
        else:
            logger.info("🔄 Обнаружены изменения (callback не настроен)")

    async def start_watching(self):
        """Запускает мониторинг файлов."""
        if self.is_running:
            logger.warning("⚠️ Watcher уже запущен")
            return
        
        self.is_running = True
        logger.info("🚀 Запуск file watcher...")
        
        try:
            # Мониторим обе папки
            folders_to_watch = [str(self.pdf_folder), str(self.other_folder)]
            
            async for changes in awatch(*folders_to_watch, recursive=False):
                if not self.is_running:
                    break
                    
                await self.handle_file_change(changes)
                
        except Exception as e:
            logger.error(f"❌ Ошибка в file watcher: {e}")
        finally:
            self.is_running = False
            logger.info("🛑 File watcher остановлен")

    def stop_watching(self):
        """Останавливает мониторинг."""
        self.is_running = False
        logger.info("🛑 Остановка file watcher...")


# Пример callback функции для перезагрузки
async def example_reload_callback():
    """Пример функции перезагрузки базы знаний."""
    logger.info("🔄 Имитация перезагрузки базы знаний...")
    await asyncio.sleep(1)  # Имитация работы
    logger.info("✅ База знаний перезагружена (пример)")


async def main():
    """Основная функция для тестирования."""
    print("=" * 60)
    print("🔍 Knowledge Base File Watcher - Тест")
    print("=" * 60)
    
    # Создаем watcher с тестовым callback
    watcher = KnowledgeWatcher(
        reload_callback=example_reload_callback,
        debounce_seconds=2.0
    )
    
    print("📁 Мониторинг папок:")
    print(f"  - PDF: {watcher.pdf_folder}")
    print(f"  - Other: {watcher.other_folder}")
    print("\n💡 Добавьте/измените файлы в этих папках для тестирования")
    print("⌨️  Нажмите Ctrl+C для остановки\n")
    
    try:
        await watcher.start_watching()
    except KeyboardInterrupt:
        print("\n🛑 Остановка по запросу пользователя")
        watcher.stop_watching()
    except Exception as e:
        print(f"\n❌ Ошибка: {e}")


if __name__ == "__main__":
    asyncio.run(main())