#!/usr/bin/env python3
"""Тест для проверки работы textract с нашими файлами."""

import os
import textract

def test_file_extraction():
    """Тестируем извлечение текста из файлов."""
    files_to_test = [
        "agent_docs/Запчасти Болгария.csv",
        "agent_docs/О компании.docx", 
        "agent_docs/КРАНЫ ГРУЗОПОДЪЕМНЫЕ Классификация режимов работы.pdf"
    ]
    
    for file_path in files_to_test:
        print(f"\n🔍 Тестируем файл: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"❌ Файл не найден: {file_path}")
            continue
            
        try:
            # Попробуем извлечь текст
            text = textract.process(file_path)
            decoded_text = text.decode('utf-8')
            
            print(f"✅ Успешно извлечен текст ({len(decoded_text)} символов)")
            print(f"📄 Превью: {decoded_text[:200]}...")
            
        except Exception as e:
            print(f"❌ Ошибка при обработке {file_path}: {e}")

if __name__ == "__main__":
    test_file_extraction()