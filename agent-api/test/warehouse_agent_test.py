#!/usr/bin/env python3
"""
Тест складского агента для работы с Google Sheets.
"""

import os
import sys
sys.path.append('..')

from dotenv import load_dotenv
from agents.warehouse_agent import get_warehouse_agent

# Загружаем переменные окружения
load_dotenv()

def test_warehouse_agent():
    """Тестируем складского агента."""
    
    print("🏭 Тестируем Warehouse Agent")
    print("=" * 60)
    
    # Создаем складского агента
    agent = get_warehouse_agent(debug_mode=True)
    
    print("\n📋 Тест 1: Просмотр товаров")
    agent.print_response("Покажи все товаров с листа 'Запчасти Болгария'")
    
    print("\n" + "="*60)
    print("✏️ Тест 2: Изменение количества")
    agent.print_response("Измени количество 'Двигатель передвижения МА 71 В-6 , F 150' на 50")
    



if __name__ == "__main__":
    test_warehouse_agent()
