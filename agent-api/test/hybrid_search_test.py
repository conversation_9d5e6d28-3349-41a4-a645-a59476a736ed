#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Hybrid search smoke-test for Avangard knowledge base.
- Checks existence of CSV file 'Запчасти Болгария.csv'
- Loads CombinedKnowledgeBase from get_avangard_knowledge()
- Prints counts and verifies the CSV is present in indexed docs
- Attempts a hybrid search for query 'запчасти болгария'
"""
import os
import sys
import json

# Ensure we import from project root when run with CWD=agent-api
BASE_DIR = os.path.dirname(__file__)
PROJECT_DIR = os.path.abspath(os.path.join(BASE_DIR, '..'))
if PROJECT_DIR not in sys.path:
    sys.path.insert(0, PROJECT_DIR)

from agents.avangard_consultant import get_avangard_knowledge

CSV_NAME = 'Запчасти Болгария.csv'
CSV_PATH = os.path.join(PROJECT_DIR, 'agent_docs_other', CSV_NAME)


def main():
    print('=== Hybrid Search Test ===')

    # 1) Check file presence on filesystem
    print(f"[FS] Expect file: {CSV_PATH}")
    if os.path.exists(CSV_PATH):
        print("[FS] ✅ File exists")
    else:
        print("[FS] ❌ File NOT found")

    # 2) Build knowledge base and LOAD it (no recreate)
    kb = get_avangard_knowledge()
    print("[KB] Loading KB (recreate=False)…")
    try:
        kb.load(recreate=False)
        print("[KB] ✅ Loaded")
    except Exception as e:
        print(f"[KB] ❌ Load failed: {e}")
    sources = getattr(kb, 'sources', [])
    print(f"[KB] Sources: {[s.__class__.__name__ for s in sources]}")

    # 3) Read indexed docs from each source if possible
    total_docs = 0
    found_csv = False
    for src in sources:
        vdb = getattr(src, 'vector_db', None)
        if vdb is None:
            print(f"[KB] ⚠️ Source {src.__class__.__name__} has no vector_db")
            continue
        read = getattr(vdb, 'read', None)
        if callable(read):
            try:
                docs = read()
                cnt = len(docs) if docs else 0
                total_docs += cnt
                print(f"[KB] {src.__class__.__name__}: {cnt} docs")
                # Try to detect our CSV by meta_data.source or name/id
                for d in docs or []:
                    meta = getattr(d, 'meta_data', {}) or {}
                    src_path = meta.get('source') or ''
                    name = getattr(d, 'name', '')
                    if CSV_NAME in str(src_path) or CSV_NAME == name:
                        found_csv = True
                        break
            except Exception as e:
                print(f"[KB] ❌ Error reading docs from {src.__class__.__name__}: {e}")
        else:
            print(f"[KB] ⚠️ {src.__class__.__name__} vector_db has no read()")

    print(f"[KB] Total docs across sources: {total_docs}")
    print(f"[KB] CSV present in index: {'✅ YES' if found_csv else '❌ NO'}")

    # 4) Try hybrid search (if API is available)
    query = 'запчасти болгария'
    alt_query = 'запчасти болагрия'
    k = 30
    print(f"[SEARCH] Query: {query}")
    search_fn = getattr(kb, 'search', None)
    if callable(search_fn):
        try:
            results = kb.search(query=query, num_documents=k)
            # results may be list of Documents or dicts
            print(f"[SEARCH] Got {len(results) if results is not None else 0} results (k={k})")
            for i, r in enumerate(results or []):
                name = getattr(r, 'name', None) or getattr(r, 'id', None) or ''
                meta = getattr(r, 'meta_data', {}) or {}
                src_path = meta.get('source')
                mark = ' <<TARGET>>' if 'Запчасти Болгария.csv' in (name or '') or 'Запчасти Болгария.csv' in str(src_path or '') else ''
                print(f"  {i+1:02d}. {name} | source={src_path}{mark}")
            # Try alt query (typo)
            results2 = kb.search(query=alt_query, num_documents=k)
            print(f"[SEARCH] Alt '{alt_query}': {len(results2) if results2 else 0} results (k={k})")
        except Exception as e:
            print(f"[SEARCH] ❌ kb.search failed: {e}")
        # Additionally try per-source search
        for src in sources:
            search_src = getattr(src, 'search', None)
            if callable(search_src):
                try:
                    res = search_src(query=query, num_documents=k)
                    hits = 0 if res is None else len(res)
                    any_target = any(
                        ('Запчасти Болгария.csv' in (getattr(r, 'name', '') or getattr(r, 'id', '') or '')) or
                        ('Запчасти Болгария.csv' in str((getattr(r, 'meta_data', {}) or {}).get('source', '')))
                        for r in (res or [])
                    )
                    print(f"[SEARCH:{src.__class__.__name__}] {hits} results (k={k}) | target={'YES' if any_target else 'NO'}")
                except Exception as e:
                    print(f"[SEARCH:{src.__class__.__name__}] ❌ failed: {e}")
    else:
        # Attempt to search on each source
        for src in sources:
            search_src = getattr(src, 'search', None)
            if callable(search_src):
                try:
                    res = src.search(query=query, num_documents=10)
                    print(f"[SEARCH:{src.__class__.__name__}] {len(res) if res else 0} results")
                except Exception as e:
                    print(f"[SEARCH:{src.__class__.__name__}] ❌ failed: {e}")
            else:
                print(f"[SEARCH:{src.__class__.__name__}] no search() API")

    print('=== Done ===')


if __name__ == '__main__':
    main()
