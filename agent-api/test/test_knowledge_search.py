#!/usr/bin/env python3
"""
Тест поиска в базе знаний агента Avangard.
"""

import os
import sys
# Добавляем корневую папку проекта в путь
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from dotenv import load_dotenv
from agents.avangard_consultant import get_avangard_consultant

# Загружаем переменные окружения
load_dotenv()

def test_knowledge_search():
    """Тестируем поиск в базе знаний."""
    
    print("🔍 Тестируем поиск в базе знаний Avangard Consultant")
    print("=" * 60)
    
    # Создаем агента
    agent = get_avangard_consultant(debug_mode=True)
    
    print("\n� Тест  1: Простой вопрос о контактах")
    agent.print_response("Какие контакты у компании Авангард?")
    
    print("\n" + "="*60)
    print("🏭 Тест 2: Вопрос о компании")
    agent.print_response("Расскажи о компании Авангард")
    
    print("\n" + "="*60)
    print("🌡️ Тест 3: Вопрос о климатических особенностях")
    agent.print_response("Какие климатические особенности Сибири влияют на работу кранов?")


if __name__ == "__main__":
    test_knowledge_search()