#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Тестовый скрипт для запроса файлов Google Drive по ID и вывода того, что возвращает API.
Расположение: /home/<USER>/My/sibavangard4/docs_api/test_drive_api.py

Назначение:
- По ID файла (любой тип: Google Docs, PDF, и т.п.) получить метаданные через Drive API.
- Для Google Docs дополнительно дернуть Docs API и вывести структуру документа (body.content).
- Для PDF/других бинарных типов показать, какие exportLinks / webContentLink доступны (что реально можно получить по API).
- Ничего не сохраняет, только печатает JSON в stdout.

Ожидания по окружению:
- В корне репо (.env) заданы:
  GOOGLE_SERVICE_ACCOUNT_FILE=meta-geography-464110-b9-55c203771d7e.json
- Файл сервисного аккаунта существует.
- В venv установлены зависимости из docs_api/requirements.txt (google-api-python-client, google-auth, google-auth-httplib2, python-dotenv).

Запуск:
  python docs_api/test_drive_api.py --id FILE_ID
Пример:
  python docs_api/test_drive_api.py --id 1TVjjkgfejjLyqfIk86rTKjKSHIs2KMik
"""

import os
import sys
import json
import argparse
from pathlib import Path

from dotenv import load_dotenv
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError


def load_credentials():
    load_dotenv()  # загрузим .env из корня проекта
    cred_path = os.getenv("GOOGLE_SERVICE_ACCOUNT_FILE")
    if not cred_path:
        raise RuntimeError("Не задан GOOGLE_SERVICE_ACCOUNT_FILE в .env")
    if not Path(cred_path).exists():
        raise RuntimeError(f"Файл сервисного аккаунта не найден: {cred_path}")

    scopes = [
        "https://www.googleapis.com/auth/drive.readonly",
        "https://www.googleapis.com/auth/documents.readonly",
        "https://www.googleapis.com/auth/spreadsheets.readonly",
    ]
    creds = service_account.Credentials.from_service_account_file(cred_path, scopes=scopes)
    return creds


def get_drive_file_metadata(drive, file_id: str):
    # Поля: включаем общий набор + ссылки на экспорт (для Google типов) и скачивание
    fields = (
        "id, name, mimeType, size, createdTime, modifiedTime, "
        "owners(displayName, emailAddress), "
        "webViewLink, webContentLink, iconLink, "
        "hasThumbnail, thumbnailLink, "
        "md5Checksum, headRevisionId, "
        "exportLinks"
    )
    return drive.files().get(fileId=file_id, fields=fields).execute()


def try_get_docs_body(docs, file_id: str):
    # Для Google Docs "application/vnd.google-apps.document"
    try:
        doc = docs.documents().get(documentId=file_id).execute()
        # Оставим только ключевые поля, чтобы не зашкаливать вывод
        minimal = {
            "documentId": doc.get("documentId"),
            "title": doc.get("title"),
            "documentStyle": doc.get("documentStyle"),
            # body.content может быть очень большим; показываем только первые элементы, скажем 10
            "body_preview": doc.get("body", {}).get("content", [])[:10],
            "body_length": len(doc.get("body", {}).get("content", [])),
            "namedStyles": doc.get("namedStyles", {}),
        }
        return minimal
    except HttpError as e:
        # Если не Google Doc или нет прав — вернем ошибку как текст
        return {"docs_error": str(e)}


def main():
    parser = argparse.ArgumentParser(description="Показать, что возвращают Google Drive/Docs API по ID файла.")
    parser.add_argument("--id", required=True, help="ID файла в Google Drive")
    args = parser.parse_args()

    try:
        creds = load_credentials()
    except Exception as e:
        print(json.dumps({"error": f"Auth error: {e}"}, ensure_ascii=False, indent=2))
        sys.exit(1)

    try:
        drive = build("drive", "v3", credentials=creds)
        docs = build("docs", "v1", credentials=creds)
    except Exception as e:
        print(json.dumps({"error": f"Build client error: {e}"}, ensure_ascii=False, indent=2))
        sys.exit(1)

    result = {"input_id": args.id}

    # 1) Drive metadata
    try:
        meta = get_drive_file_metadata(drive, args.id)
        result["drive_metadata"] = meta
    except HttpError as e:
        result["drive_metadata_error"] = str(e)

    # 2) Если это Google Docs — попробуем вытянуть структуру
    mime = result.get("drive_metadata", {}).get("mimeType")
    if mime == "application/vnd.google-apps.document":
        result["docs_preview"] = try_get_docs_body(docs, args.id)
    else:
        result["docs_preview"] = {"note": "Не Google Docs, Docs API не применяется."}

    # 3) Экспортные ссылки и варианты
    #   - Для Google доков: exportLinks может содержать application/pdf, text/plain и т.д.
    #   - Для бинарников (PDF): webContentLink доступен если файл не Google native.
    export_links = result.get("drive_metadata", {}).get("exportLinks")
    if export_links:
        result["export_links_available"] = export_links
    else:
        result["export_links_available"] = {}

    # Выводим аккуратно
    print(json.dumps(result, ensure_ascii=False, indent=2))


if __name__ == "__main__":
    main()
