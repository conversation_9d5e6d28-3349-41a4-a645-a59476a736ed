#!/usr/bin/env python3
"""
Скачивание всех листов из Google Spreadsheet как отдельные CSV файлы.
"""

import os
import io
from pathlib import Path
from googleapiclient.discovery import build
from google.oauth2 import service_account
from googleapiclient.http import MediaIoBaseDownload
from dotenv import load_dotenv
import re

# Загружаем переменные окружения
load_dotenv()

class GoogleSheetsDownloader:
    def __init__(self):
        self.service_account_file = os.getenv('GOOGLE_SERVICE_ACCOUNT_FILE')
        self.spreadsheet_id = os.getenv('GOOGLE_SPREADSHEET_ID')
        
        if not self.service_account_file or not self.spreadsheet_id:
            raise ValueError("Не найдены GOOGLE_SERVICE_ACCOUNT_FILE или GOOGLE_SPREADSHEET_ID в .env")
        
        # Настройка аутентификации
        credentials = service_account.Credentials.from_service_account_file(
            self.service_account_file,
            scopes=['https://www.googleapis.com/auth/drive.readonly', 'https://www.googleapis.com/auth/spreadsheets.readonly']
        )
        
        self.drive_service = build('drive', 'v3', credentials=credentials)
        self.sheets_service = build('sheets', 'v4', credentials=credentials)
        
        # Создаем папку для CSV файлов
        self.csv_folder = Path("agent_docs_other")
        self.csv_folder.mkdir(exist_ok=True)
        
        print(f"📁 CSV файлы будут сохранены в: {self.csv_folder}")

    def sanitize_filename(self, filename):
        """Очищает название файла от недопустимых символов."""
        # Заменяем недопустимые символы на подчеркивания
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Убираем лишние пробелы и точки в конце
        filename = filename.strip('. ')
        # Ограничиваем длину
        if len(filename) > 100:
            filename = filename[:100]
        return filename

    def get_spreadsheet_info(self):
        """Получает информацию о таблице и всех её листах."""
        print(f"🔍 Получаем информацию о таблице: {self.spreadsheet_id}")
        
        try:
            # Получаем метаданные таблицы
            spreadsheet = self.sheets_service.spreadsheets().get(
                spreadsheetId=self.spreadsheet_id
            ).execute()
            
            title = spreadsheet.get('properties', {}).get('title', 'Unknown')
            sheets = spreadsheet.get('sheets', [])
            
            print(f"📊 Название таблицы: {title}")
            print(f"📋 Найдено листов: {len(sheets)}")
            
            sheet_info = []
            for sheet in sheets:
                sheet_properties = sheet.get('properties', {})
                sheet_id = sheet_properties.get('sheetId')
                sheet_title = sheet_properties.get('title')
                
                sheet_info.append({
                    'id': sheet_id,
                    'title': sheet_title
                })
                
                print(f"  - {sheet_title} (ID: {sheet_id})")
            
            return title, sheet_info
            
        except Exception as e:
            print(f"❌ Ошибка при получении информации о таблице: {e}")
            return None, []

    def download_sheet_as_csv(self, sheet_id, sheet_title):
        """Скачивает конкретный лист как CSV файл."""
        try:
            # Экспортируем лист как CSV
            request = self.drive_service.files().export_media(
                fileId=self.spreadsheet_id,
                mimeType='text/csv'
            )
            
            # Добавляем параметр для конкретного листа
            request.uri += f'&gid={sheet_id}'
            
            # Скачиваем данные
            file_io = io.BytesIO()
            downloader = MediaIoBaseDownload(file_io, request)
            
            done = False
            while done is False:
                status, done = downloader.next_chunk()
            
            return file_io.getvalue()
            
        except Exception as e:
            print(f"❌ Ошибка при скачивании листа {sheet_title}: {e}")
            return None

    def save_csv_file(self, content, sheet_title):
        """Сохраняет CSV файл."""
        if not content:
            return False
        
        # Очищаем название файла
        safe_filename = self.sanitize_filename(sheet_title)
        file_name = f"{safe_filename}.csv"
        file_path = self.csv_folder / file_name
        
        try:
            with open(file_path, 'wb') as f:
                f.write(content)
            
            file_size = len(content)
            print(f"✅ CSV: {file_name} ({file_size} bytes) → {file_path}")
            return True
            
        except Exception as e:
            print(f"❌ Ошибка при сохранении {file_name}: {e}")
            return False

    def download_all_sheets(self):
        """Скачивает все листы из таблицы как отдельные CSV файлы."""
        print("🚀 Начинаем скачивание листов из Google Spreadsheet...")
        
        # Получаем информацию о таблице
        spreadsheet_title, sheets_info = self.get_spreadsheet_info()
        
        if not sheets_info:
            print("⚠️ Листы не найдены!")
            return
        
        downloaded_count = 0
        
        print(f"\n📥 Скачиваем {len(sheets_info)} листов...")
        
        for sheet_info in sheets_info:
            sheet_id = sheet_info['id']
            sheet_title = sheet_info['title']
            
            print(f"\n🔄 Обрабатываем лист: {sheet_title}")
            
            # Скачиваем лист
            content = self.download_sheet_as_csv(sheet_id, sheet_title)
            
            if content:
                # Сохраняем файл
                if self.save_csv_file(content, sheet_title):
                    downloaded_count += 1
        
        print(f"\n🎉 Скачивание завершено!")
        print(f"📊 Всего скачано: {downloaded_count} листов из {len(sheets_info)}")
        print(f"📁 Файлы сохранены в: {self.csv_folder}")

def main():
    """Основная функция."""
    print("=" * 60)
    print("📊 Google Sheets Downloader для агента Avangard")
    print("=" * 60)
    
    try:
        downloader = GoogleSheetsDownloader()
        downloader.download_all_sheets()
        
    except Exception as e:
        print(f"❌ Критическая ошибка: {e}")
        return 1
    
    print("\n✅ Готово! Все листы скачаны как отдельные CSV файлы.")
    print("Теперь можно перезапустить агента для обновления базы знаний.")
    
    return 0

if __name__ == "__main__":
    exit(main())