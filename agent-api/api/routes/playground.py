from agno.playground import Playground

# from agents.agno_assist import get_agno_assist  # Временно отключен
# from agents.finance_agent import get_finance_agent  # Временно отключен
# from agents.web_agent import get_web_agent  # Временно отключен
from agents.avangard_consultant import get_avangard_consultant
from agents.warehouse_agent import get_warehouse_agent

######################################################
## Routes for the Playground Interface
######################################################

# Get Agents to serve in the playground
# web_agent = get_web_agent(debug_mode=True)  # Временно отключен
# agno_assist = get_agno_assist(debug_mode=True)  # Временно отключен
# finance_agent = get_finance_agent(debug_mode=True)  # Временно отключен
avangard_consultant = get_avangard_consultant(debug_mode=True)
warehouse_agent = get_warehouse_agent(debug_mode=True)

# Create a playground instance
playground = Playground(agents=[
    # web_agent,  # Временно отключен
    # agno_assist,  # Временно отключен
    # finance_agent,  # Временно отключен
    avangard_consultant,
    warehouse_agent
])
# Get the router for the playground
playground_router = playground.get_async_router()

