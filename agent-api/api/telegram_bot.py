import os
import asyncio
import logging
from typing import Optional

from aiogram import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, types
from aiogram.filters import Command
from aiogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from aiogram.webhook.aiohttp_server import SimpleRequestHand<PERSON>, setup_application
from aiohttp import web

# Конфигурация
ADMIN_IDS = [1828473337,6869495438,222805095]  # Telegram ID
PROJECT_PATH = "/home/<USER>/My/sibavangard4"

# НОВАЯ ФУНКЦИЯ ИНИЦИАЛИЗАЦИИ MCP
async def initialize_mcp_for_telegram():
    """Инициализация MCP tools для Telegram бота"""
    from agno.tools.mcp import MCPTools
    from mcp import StdioServerParameters
    
    # MCP server parameters (те же что в playground.py)
    server_params = StdioServerParameters(
        command=f"{PROJECT_PATH}/agno_env/bin/python",
        args=[f"{PROJECT_PATH}/mcp_server.py"]
    )
    
    try:
        print("🚀 Инициализация MCP tools для Telegram...", flush=True)
        
        # Создаем и инициализируем MCP tools
        mcp_tools = MCPTools(server_params=server_params)
        await mcp_tools.__aenter__()
        
        print("✅ MCP tools для Telegram подключены!", flush=True)
        print(f"🔧 Доступные MCP функции: {list(mcp_tools.functions.keys())}", flush=True)
        
        return mcp_tools
        
    except Exception as e:
        print(f"❌ Ошибка инициализации MCP tools: {e}", flush=True)
        import traceback
        traceback.print_exc()
        return None

# Импорт агентов ПОСЛЕ функции инициализации
from playground import consultant_agent, mcp_agent

# Глобальные переменные для агентов
CONSULTANT_AGENT = None
MCP_AGENT = None

# Словарь для хранения состояния пользователей (какой агент активен)
user_states = {}

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def markdown_to_html(text: str) -> str:
    """Конвертирует Markdown в HTML"""
    import re
    # **текст** -> <b>текст</b> (СНАЧАЛА двойные звездочки)
    text = re.sub(r'\*\*([^*]+)\*\*', r'<b>\1</b>', text)
    # *текст* -> <b>текст</b> (ПОТОМ одинарные звездочки)
    text = re.sub(r'\*([^*]+)\*', r'<b>\1</b>', text)
    # _текст_ -> <i>текст</i>
    text = re.sub(r'_([^_]+)_', r'<i>\1</i>', text)
    return text

class AvangardBot:
    def __init__(self, token: str, consultant_agent, mcp_agent):
        self.bot = Bot(token=token)
        self.dp = Dispatcher()
        
        # Используем переданных агентов
        self.consultant_agent = consultant_agent
        self.mcp_agent = mcp_agent
        
        self.setup_handlers()
    
    def is_admin(self, user_id: int) -> bool:
        """Проверяет, является ли пользователь администратором"""
        return user_id in ADMIN_IDS
    
    def get_current_agent(self, user_id: int):
        """Получает текущего активного агента для пользователя"""
        # По умолчанию - консультант
        agent_type = user_states.get(user_id, 'consultant')
        if agent_type == 'consultant':
            return self.consultant_agent
        else:
            return self.mcp_agent
    
    def set_user_agent(self, user_id: int, agent_type: str):
        """Устанавливает активного агента для пользователя"""
        user_states[user_id] = agent_type
        logger.info(f"👤 Пользователь {user_id} переключился на {agent_type} агента")
    
    async def run_shell_command(self, command: str) -> str:
        """Выполняет shell команду асинхронно"""
        try:
            process = await asyncio.create_subprocess_shell(
                command,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                return stdout.decode('utf-8')
            else:
                raise Exception(f"Command failed: {stderr.decode('utf-8')}")
                
        except Exception as e:
            logger.error(f"Shell command error: {e}")
            raise e
    
    async def update_all_data(self, message: Message):
        """Полное обновление всех данных"""
        try:
            await message.answer("📁 Собираю документы из Google Docs...")
            await self.run_shell_command(f"cd {PROJECT_PATH}/docs_api && python collect_docs.py")
            
            await message.answer("💰 Собираю прайс-листы из Google Sheets...")
            await self.run_shell_command(f"cd {PROJECT_PATH}/docs_api && python collect_pricelist.py")
            
            await message.answer("🔄 Загружаю данные в базу знаний...")
            await self.run_shell_command(f"cd {PROJECT_PATH} && python load_knowledge.py")
            
            await message.answer("✅ Полное обновление данных завершено!")
            
        except Exception as e:
            logger.error(f"❌ Ошибка обновления данных: {e}")
            await message.answer(f"❌ Ошибка при обновлении: {str(e)}")
    
    async def update_docs_only(self, message: Message):
        """Обновление только документов"""
        try:
            await message.answer("📁 Собираю документы из Google Docs...")
            await self.run_shell_command(f"cd {PROJECT_PATH}/docs_api && python collect_docs.py")
            
            await message.answer("🔄 Загружаю в базу знаний...")
            await self.run_shell_command(f"cd {PROJECT_PATH} && python load_knowledge.py")
            
            await message.answer("✅ Документы обновлены!")
            
        except Exception as e:
            logger.error(f"❌ Ошибка обновления документов: {e}")
            await message.answer(f"❌ Ошибка: {str(e)}")
    
    async def update_pricelist_only(self, message: Message):
        """Обновление только прайс-листов"""
        try:
            await message.answer("💰 Собираю прайс-листы из Google Sheets...")
            await self.run_shell_command(f"cd {PROJECT_PATH}/docs_api && python collect_pricelist.py")
            
            await message.answer("🔄 Загружаю в базу знаний...")
            await self.run_shell_command(f"cd {PROJECT_PATH} && python load_knowledge.py")
            
            await message.answer("✅ Прайс-листы обновлены!")
            
        except Exception as e:
            logger.error(f"❌ Ошибка обновления прайс-листов: {e}")
            await message.answer(f"❌ Ошибка: {str(e)}")
    
    def setup_handlers(self):
        """Настройка обработчиков сообщений"""
        
        @self.dp.message(Command("start"))
        async def start_handler(message: Message):
            """Обработчик команды /start"""
            user_id = message.from_user.id
            user_name = message.from_user.first_name or "Пользователь"
            username = message.from_user.username or "без_username"
            
            print(f"🚀 /start - User ID: {user_id}, Username: @{username}, Name: {user_name}", flush=True)
            
            welcome_text = self.get_welcome_message(user_name)
            keyboard = self.get_agent_selection_keyboard()
            
            await message.answer(
                welcome_text,
                parse_mode="Markdown",
                disable_web_page_preview=True,
                reply_markup=keyboard
            )
        
        @self.dp.message(Command("consultant"))
        async def consultant_handler(message: Message):
            """Переключение на консультанта"""
            user_id = message.from_user.id
            user_name = message.from_user.first_name or "Пользователь"
            username = message.from_user.username or "без_username"
            
            print(f"🤖 /consultant - User ID: {user_id}, Username: @{username}, Name: {user_name}", flush=True)
            
            self.set_user_agent(message.from_user.id, 'consultant')
            await message.answer(
                "🤖 *Переключено на КОНСУЛЬТАНТА*\n\n"
                "Теперь я помогу с информацией о компании, товарах и услугах из базы знаний.",
                parse_mode="Markdown"
            )
        
        @self.dp.message(Command("mcp"))
        async def mcp_handler(message: Message):
            """Переключение на администратора"""
            user_id = message.from_user.id
            user_name = message.from_user.first_name or "Пользователь"
            username = message.from_user.username or "без_username"
            
            print(f"⚙️ /mcp - User ID: {user_id}, Username: @{username}, Name: {user_name}", flush=True)
            
            self.set_user_agent(message.from_user.id, 'mcp')
            await message.answer(
                "⚙️ *Переключено на АДМИНИСТРАТОРА*\n\n"
                "Теперь я помогу с редактированием прайс-листов и управлением данными в таблицах.",
                parse_mode="Markdown"
            )
        
        @self.dp.message(Command("status"))
        async def status_handler(message: Message):
            """Показать текущий активный агент"""
            user_id = message.from_user.id
            user_name = message.from_user.first_name or "Пользователь"
            username = message.from_user.username or "без_username"
            
            print(f"📊 /status - User ID: {user_id}, Username: @{username}, Name: {user_name}", flush=True)
            
            current_agent_type = user_states.get(message.from_user.id, 'consultant')
            agent_name = "🤖 Консультант" if current_agent_type == 'consultant' else "⚙️ MCP Администратор"
            
            await message.answer(
                f"📊 *Текущий активный помощник:* {agent_name}\n\n"
                f"Используйте /consultant или /mcp для переключения.",
                parse_mode="Markdown"
            )
        
        # АДМИНСКИЕ КОМАНДЫ
        @self.dp.message(Command("update_data"))
        async def update_data_handler(message: Message):
            """Обновление всех данных: сбор из Google + загрузка в базу знаний"""
            user_id = message.from_user.id
            user_name = message.from_user.first_name or "Пользователь"
            username = message.from_user.username or "без_username"
            
            print(f"🔄 /update_data - User ID: {user_id}, Username: @{username}, Name: {user_name}", flush=True)
            
            if not self.is_admin(message.from_user.id):
                await message.answer("❌ У вас нет прав для выполнения этой команды")
                return
            
            await message.answer("🔄 Начинаю полное обновление данных...")
            await self.update_all_data(message)

        # @self.dp.message(Command("update_docs"))
        # async def update_docs_handler(message: Message):
        #     """Обновление только документов"""
        #     user_id = message.from_user.id
        #     user_name = message.from_user.first_name or "Пользователь"
        #     username = message.from_user.username or "без_username"
        #     
        #     print(f"📁 /update_docs - User ID: {user_id}, Username: @{username}, Name: {user_name}", flush=True)
        #     
        #     if not self.is_admin(message.from_user.id):
        #         await message.answer("❌ У вас нет прав для выполнения этой команды")
        #         return
        #     
        #     await self.update_docs_only(message)

        # @self.dp.message(Command("update_pricelist"))
        # async def update_pricelist_handler(message: Message):
        #     """Обновление только прайс-листов"""
        #     user_id = message.from_user.id
        #     user_name = message.from_user.first_name or "Пользователь"
        #     username = message.from_user.username or "без_username"
        #     
        #     print(f"💰 /update_pricelist - User ID: {user_id}, Username: @{username}, Name: {user_name}", flush=True)
        #     
        #     if not self.is_admin(message.from_user.id):
        #         await message.answer("❌ У вас нет прав для выполнения этой команды")
        #         return
        #     
        #     await self.update_pricelist_only(message)

        @self.dp.message(Command("admin"))
        async def admin_handler(message: Message):
            """Админ панель"""
            user_id = message.from_user.id
            user_name = message.from_user.first_name or "Пользователь"
            username = message.from_user.username or "без_username"
            
            print(f"⚙️ /admin - User ID: {user_id}, Username: @{username}, Name: {user_name}", flush=True)
            
            if not self.is_admin(message.from_user.id):
                await message.answer("❌ У вас нет прав супер админа")
                return
            
            keyboard = InlineKeyboardMarkup(inline_keyboard=[
                [InlineKeyboardButton(text="🔄 Обновить базу", callback_data="admin_update_all")],
                # [
                #     InlineKeyboardButton(text="📁 Документы", callback_data="admin_update_docs"),
                #     InlineKeyboardButton(text="💰 Прайсы", callback_data="admin_update_pricelist")
                # ],
                [InlineKeyboardButton(text="📊 Статус системы", callback_data="admin_status")]
            ])
            
            await message.answer("⚙️ *Админ панель*", parse_mode="Markdown", reply_markup=keyboard)
        
        @self.dp.callback_query(lambda c: c.data.startswith("agent_"))
        async def agent_callback_handler(callback: CallbackQuery):
            """Обработчик нажатий на кнопки выбора агента"""
            user_id = callback.from_user.id
            user_name = callback.from_user.first_name or "Пользователь"
            username = callback.from_user.username or "без_username"
            
            agent_type = callback.data.split("_")[1]  # agent_consultant -> consultant
            print(f"🔄 Выбор агента {agent_type} - User ID: {user_id}, Username: @{username}, Name: {user_name}", flush=True)
            
            self.set_user_agent(callback.from_user.id, agent_type)
            
            if agent_type == 'consultant':
                response_text = "🤖 *Выбран КОНСУЛЬТАНТ*\n\nГотов помочь с информацией о компании и товарах!"
            else:
                response_text = "⚙️ *Выбран АДМИНИСТРАТОР*\n\nГотов помочь с редактированием данных и прайс-листов!"
            
            await callback.message.edit_text(
                response_text,
                parse_mode="Markdown"
            )
            await callback.answer()
        
        @self.dp.callback_query(lambda c: c.data.startswith("admin_"))
        async def admin_callback_handler(callback: CallbackQuery):
            """Обработчик кнопок админ панели"""
            if not self.is_admin(callback.from_user.id):
                await callback.answer("❌ Нет прав", show_alert=True)
                return
            
            action = callback.data.replace("admin_", "")
            
            if action == "update_all":
                await callback.message.edit_text("🔄 Запускаю полное обновление...")
                await self.update_all_data(callback.message)
            elif action == "update_docs":
                await callback.message.edit_text("📁 Обновляю документы...")
                await self.update_docs_only(callback.message)
            elif action == "update_pricelist":
                await callback.message.edit_text("💰 Обновляю прайс-листы...")
                await self.update_pricelist_only(callback.message)
            elif action == "status":
                status_text = f"📊 *Статус системы*\n\n" \
                             f"🔧 Агенты: активны\n" \
                             f"📁 База знаний: готова\n" \
                             f"⚙️ MCP сервер: подключен\n" \
                             f"👥 Активных пользователей: {len(user_states)}"
                await callback.message.edit_text(status_text, parse_mode="Markdown")
            
            await callback.answer()
        
        @self.dp.message(lambda message: message.content_type == "text")
        async def text_handler(message: Message):
            """Обработчик текстовых сообщений"""
            # Запускаем обработку в фоне, чтобы не блокировать webhook
            task = asyncio.create_task(self.process_user_message(message))
            task.add_done_callback(lambda t: logger.error(f"Task error: {t.exception()}") if t.exception() else None)
        
        @self.dp.message(lambda message: message.content_type == "voice")
        async def voice_handler(message: Message):
            """Обработчик голосовых сообщений"""
            # Запускаем обработку в фоне, чтобы не блокировать webhook
            task = asyncio.create_task(self.process_voice_message(message))
            task.add_done_callback(lambda t: logger.error(f"Voice task error: {t.exception()}") if t.exception() else None)
        
        @self.dp.message(lambda message: message.content_type == "audio")
        async def audio_handler(message: Message):
            """Обработчик аудиофайлов"""
            # Запускаем обработку в фоне, чтобы не блокировать webhook
            task = asyncio.create_task(self.process_voice_message(message))
            task.add_done_callback(lambda t: logger.error(f"Audio task error: {t.exception()}") if t.exception() else None)
    
    def get_welcome_message(self, user_name: str) -> str:
        """Создает приветственное сообщение с поддержкой Markdown"""
        return f"""🏭 *Добро пожаловать в Новосибирский крановый завод Авангард, {user_name}!*

У нас есть два специализированных помощника:

  🤖 *КОНСУЛЬТАНТ* (активен по умолчанию)
  🏢 Информация о компании из базы знаний
  🔍 Поиск информации о товарах и услугах
  🌐 Актуальная информация из интернета
  🎤 Голосовые команды

  ⚙️ *АДМИНИСТРАТОР*
  📊 Редактирование прайс-листов и таблиц
  💰 Обновление цен и остатков
   ✏️ Управление данными склада
  📋 Массовые операции с товарами
  🎤 Голосовые команды для редактирования

*Выберите помощника:*"""
    
    def get_agent_selection_keyboard(self):
        """Создает клавиатуру для выбора агента"""
        keyboard = InlineKeyboardMarkup(inline_keyboard=[
            [
                InlineKeyboardButton(
                    text="🤖 Консультант", 
                    callback_data="agent_consultant"
                ),
                InlineKeyboardButton(
                    text="⚙️ Администратор", 
                    callback_data="agent_mcp"
                )
            ]
        ])
        return keyboard
    
    async def process_user_message(self, message: Message):
        """Обрабатывает текстовое сообщение пользователя"""
        try:
            # Показываем индикатор печати
            await message.bot.send_chat_action(message.chat.id, "typing")
            
            user_id = message.from_user.id
            user_name = message.from_user.first_name or "Пользователь"
            username = message.from_user.username or "без_username"
            user_text = message.text
            
            print(f"💬 Текстовое сообщение - User ID: {user_id}, Username: @{username}, Name: {user_name}, Text: {user_text[:50]}...", flush=True)
            logger.info(f"📥 Получен запрос от {user_name}: {user_text}")
            
            # Показываем что работаем
            preparing_msg = await message.answer("🤖 Подготавливаю ответ...")
            
            # Получаем текущего активного агента для пользователя
            current_agent = self.get_current_agent(message.from_user.id)
            agent_type = user_states.get(message.from_user.id, 'consultant')
            agent_name = "🤖 Консультант" if agent_type == 'consultant' else "⚙️ MCP Администратор"
            
            logger.info(f"🎯 Используется агент: {agent_name}")
            
            # Запускаем текущего агента
            if hasattr(current_agent, 'arun'):
                response = await current_agent.arun(user_text)
            else:
                response = await asyncio.to_thread(current_agent.run, user_text)
            
            # Удаляем сообщение о подготовке
            try:
                await preparing_msg.delete()
            except:
                pass
            
            logger.info(f"🤖 Ответ агента: {response.content[:100]}...")
            
            # Отправляем ответ с поддержкой HTML и отключением предпросмотра
            logger.info("📤 Отправляю ответ пользователю...")
            
            # Конвертируем Markdown в HTML для всех агентов
            response_text = markdown_to_html(response.content)
            
            await message.answer(
                response_text,
                parse_mode="HTML",
                disable_web_page_preview=True
            )
            
            logger.info("✅ Сообщение успешно отправлено")
                
        except Exception as e:
            logger.error(f"❌ Ошибка обработки текстового сообщения: {e}")
            await message.answer("Произошла ошибка при обработке сообщения. Попробуйте еще раз.")
    
    async def process_voice_message(self, message: Message):
        """Обрабатывает голосовое сообщение пользователя"""
        try:
            # Показываем индикатор печати
            await message.bot.send_chat_action(message.chat.id, "typing")
            
            user_id = message.from_user.id
            user_name = message.from_user.first_name or "Пользователь"
            username = message.from_user.username or "без_username"
            
            print(f"🎤 Голосовое сообщение - User ID: {user_id}, Username: @{username}, Name: {user_name}", flush=True)
            logger.info(f"🎤 Получено голосовое сообщение от {user_name}")
            
            # Получаем голосовое сообщение или аудио
            voice_file = message.voice or message.audio
            if not voice_file:
                await message.answer("❌ Не удалось получить голосовое сообщение")
                return
            
            # Скачиваем аудиофайл
            audio_path = await self.download_voice_file(voice_file.file_id)
            if not audio_path:
                await message.answer("❌ Не удалось скачать голосовое сообщение")
                return
            
            logger.info(f"📁 Голосовое сообщение сохранено: {audio_path}")
            
            # Получаем текущего активного агента для пользователя
            current_agent = self.get_current_agent(message.from_user.id)
            agent_type = user_states.get(message.from_user.id, 'consultant')
            agent_name = "🤖 Консультант" if agent_type == 'consultant' else "⚙️ MCP Администратор"
            
            logger.info(f"🎯 Голосовое сообщение обрабатывается агентом: {agent_name}")
            
            # Находим OpenAITools среди инструментов текущего агента
            openai_tool = None
            for tool in current_agent.tools:
                if hasattr(tool, 'transcription_model'):
                    openai_tool = tool
                    break
            
            if not openai_tool:
                await message.answer("❌ Функция распознавания речи недоступна")
                return
            
            # Транскрибируем аудио в текст асинхронно
            logger.info("🔄 Транскрибируем голосовое сообщение...")
            
            # Проверяем, есть ли асинхронный метод транскрипции
            if hasattr(openai_tool, 'atranscribe_audio'):
                transcribed_text = await openai_tool.atranscribe_audio(audio_path)
            else:
                # Fallback на синхронный метод (но это блокирует event loop)
                transcribed_text = openai_tool.transcribe_audio(audio_path)
            
            if not transcribed_text or "Failed to transcribe" in transcribed_text:
                await message.answer("❌ Не удалось распознать речь. Попробуйте еще раз.")
                return
            
            logger.info(f"🎤 Транскрипция: {transcribed_text}")
            
            # Отправляем транскрипцию пользователю
            await message.answer(
                f"🎤 *Распознано:* {transcribed_text}",
                parse_mode="Markdown",
                disable_web_page_preview=True
            )
            
            # Обрабатываем транскрипцию через агента
            logger.info("🤖 Обрабатываю транскрипцию через агента...")
            preparing_msg = await message.answer("🤖 Подготавливаю ответ...")
            
            # Запускаем текущего агента
            if hasattr(current_agent, 'arun'):
                response = await current_agent.arun(transcribed_text)
            else:
                response = await asyncio.to_thread(current_agent.run, transcribed_text)
            
            # Удаляем сообщение о подготовке
            try:
                await preparing_msg.delete()
            except:
                pass
            
            logger.info(f"🤖 Ответ агента на голосовое: {response.content[:100]}...")
            
            # Отправляем ответ агента
            # Конвертируем Markdown в HTML для всех агентов
            response_text = markdown_to_html(response.content)
            
            await message.answer(
                response_text,
                parse_mode="HTML",
                disable_web_page_preview=True
            )
            
            # Удаляем временный файл
            try:
                os.remove(audio_path)
                logger.info(f"🗑️ Временный файл удален: {audio_path}")
            except:
                pass
                
        except Exception as e:
            logger.error(f"❌ Ошибка обработки голосового сообщения: {e}")
            await message.answer("❌ Произошла ошибка при обработке голосового сообщения")
    
    async def download_voice_file(self, file_id: str) -> Optional[str]:
        """Скачивает голосовой файл от Telegram"""
        try:
            # Получаем информацию о файле
            file_info = await self.bot.get_file(file_id)
            
            # Скачиваем файл
            temp_filename = f"temp_voice_{file_id}.ogg"
            await self.bot.download_file(file_info.file_path, temp_filename)
            
            return temp_filename
            
        except Exception as e:
            logger.error(f"❌ Ошибка скачивания голосового файла: {e}")
            return None
    
    def get_app(self):
        """Создает aiohttp приложение для webhook"""
        app = web.Application()
        
        # Настройка webhook handler
        webhook_requests_handler = SimpleRequestHandler(
            dispatcher=self.dp,
            bot=self.bot,
        )
        webhook_requests_handler.register(app, path="/telegram/webhook")
        
        # Health check endpoint
        async def health_check(request):
            return web.json_response({"status": "Telegram bot is running"})
        
        app.router.add_get("/", health_check)
        
        setup_application(app, self.dp, bot=self.bot)
        return app
    
    async def start_webhook(self, webhook_url: str):
        """Устанавливает webhook"""
        await self.bot.set_webhook(
            url=webhook_url,
            drop_pending_updates=True
        )
        logger.info(f"✅ Webhook установлен: {webhook_url}")
    
    async def start_polling(self):
        """Запускает бота в режиме polling"""
        await self.bot.delete_webhook(drop_pending_updates=True)
        logger.info("🚀 Бот запущен в режиме polling")
        await self.dp.start_polling(self.bot)
    
    async def serve_webhook_async(self, port: int = 8080, webhook_url: str = None):
        """Запускает бота в режиме webhook (асинхронно)"""
        if webhook_url:
            await self.start_webhook(webhook_url)
        
        app = self.get_app()
        
        logger.info(f"🤖 Telegram Bot запущен на порту {port}")
        logger.info(f"📡 Webhook URL: http://localhost:{port}/telegram/webhook")
        
        runner = web.AppRunner(app)
        await runner.setup()
        
        site = web.TCPSite(runner, "0.0.0.0", port)
        await site.start()
        
        # Держим сервер запущенным
        try:
            await asyncio.Future()  # run forever
        except KeyboardInterrupt:
            logger.info("🛑 Остановка бота...")
        finally:
            await runner.cleanup()

# В main функции:
if __name__ == "__main__":
    async def main():
        telegram_token = os.getenv("TELEGRAM_TOKEN", "**********************************************")
        
        # 1. Инициализируем MCP tools
        mcp_tools = await initialize_mcp_for_telegram()
        
        # 2. Добавляем MCP tools к MCP агенту
        if mcp_tools:
            mcp_agent.tools = [mcp_tools] + mcp_agent.tools
            print(f"📋 MCP tools добавлены к MCP агенту. Всего инструментов: {len(mcp_agent.tools)}")
        
        # 3. Создаем бота с готовыми агентами
        bot = AvangardBot(token=telegram_token, consultant_agent=consultant_agent, mcp_agent=mcp_agent)
        
        # 4. Запускаем webhook
        webhook_url = "https://closing-pup-flying.ngrok-free.app/telegram/webhook"
        await bot.serve_webhook_async(port=8080, webhook_url=webhook_url)
    
    # Запускаем асинхронно
    asyncio.run(main())