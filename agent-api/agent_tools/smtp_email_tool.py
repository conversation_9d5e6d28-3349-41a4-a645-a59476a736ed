import os
from dotenv import load_dotenv
load_dotenv()
import smtplib
from email.mime.text import MIMEText

def smtp_email(to: str, subject: str, body: str) -> str:
    smtp_server = "devcode.su"
    smtp_port = 587
    from_email = "<EMAIL>"
    from_name = "Avangard Agent"
    from_password = "MuiPPhykwOEBsJ8n"

    msg = MIMEText(body)
    msg["Subject"] = subject
    msg["From"] = f"{from_name} <{from_email}>"
    msg["To"] = to

    try:
        server = smtplib.SMTP(smtp_server, smtp_port)
        server.starttls()
        server.login(from_email, from_password)
        server.sendmail(from_email, to, msg.as_string())
        server.quit()
        return f"✅ Email успешно отправлен на {to} с темой '{subject}'"
    except Exception as e:
        return f"❌ Ошибка отправки email: {e}"

