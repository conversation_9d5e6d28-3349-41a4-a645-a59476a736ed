from typing import Optional
from agno.agent import Agent, AgentKnowledge
from agno.models.openai import OpenAIChat
from agno.memory.v2.db.postgres import PostgresMemoryDb
from agno.memory.v2.memory import Memory
from agno.storage.agent.postgres import PostgresAgentStorage
from agno.embedder.openai import OpenAIEmbedder
from agno.knowledge.document import DocumentKnowledgeBase
from agno.knowledge.pdf import PDFKnowledgeBase, PDFReader  
from agno.knowledge.combined import CombinedKnowledgeBase
from agno.vectordb.pgvector import PgVector, SearchType
from agno.tools.exa import ExaTools
from agent_tools.smtp_email_tool import smtp_email
import importlib
import agent_prompts.consultant_instructions as consultant_module
from agno.tools.openai import OpenAITools
from db.session import db_url
import os


def load_consultant_instructions():
    """Горячая перезагрузка инструкций консультанта без рестарта процесса.
    Возвращает список инструкций, совместимый с Agent(..., instructions=...)
    """
    # Перезагружаем модуль с инструкциями, чтобы подтянуть свежие изменения
    importlib.reload(consultant_module)
    text = getattr(consultant_module, 'CONSULTANT_INSTRUCTIONS', '')
    return text.split('\n\n') if isinstance(text, str) else text


def get_avangard_knowledge() -> CombinedKnowledgeBase:
    """Создает комбинированную базу знаний - точно по документации Agno."""
    
    # PDF документы (ГОСТы и пр.) — используем стандартный PDFReader без кастомного чанкинга
    pdf_reader = PDFReader()
    pdf_kb = PDFKnowledgeBase(
        path="agent_docs_pdf",
        pdf_reader=pdf_reader,
        vector_db=PgVector(
            table_name="avangard_pdf_knowledge",
            db_url=db_url,
            search_type=SearchType.hybrid, 
        ),
        num_documents=10,
    )
    
    # Документы (CSV, DOCX, etc.) — используем встроенную загрузку Agno по path, без ручного дублирования
    doc_folder = "agent_docs_other"
    if os.path.exists(doc_folder):
        print(f"🔎 Scanning other docs directory: {doc_folder}")
        files = [f for f in os.listdir(doc_folder) if os.path.isfile(os.path.join(doc_folder, f))]
        for f in files:
            fp = os.path.join(doc_folder, f)
            try:
                print(f"  - {f} | {os.path.getsize(fp)} bytes")
            except Exception:
                print(f"  - {f}")
    else:
        print(f"❌ Directory '{doc_folder}' not found!")

    doc_kb = DocumentKnowledgeBase(
        path="agent_docs_other",
        vector_db=PgVector(
            table_name="avangard_doc_knowledge", 
            db_url=db_url,
            search_type=SearchType.hybrid, 
        ),
        documents=[],
        num_documents=15,
    )
    
    # Комбинированная база знаний - БЕЗ принудительной загрузки здесь!
    return CombinedKnowledgeBase(
        sources=[pdf_kb, doc_kb],
        vector_db=PgVector(
            table_name="avangard_combined_knowledge",
            db_url=db_url,
            search_type=SearchType.hybrid, 
        ),
        num_documents=20,
    )


def get_avangard_consultant(
    model_id: str = "gpt-4o-mini",
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    debug_mode: bool = True,
):
    """Фабрика агента Avangard Consultant для FastAPI API с DocumentKnowledgeBase."""
    
    # Настройка Google Sheets credentials
    google_credentials_path = os.getenv('GOOGLE_CREDENTIALS_PATH', 'meta-geography-464110-b9-55c203771d7e.json')
    if google_credentials_path and os.path.exists(google_credentials_path):
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = google_credentials_path
        if debug_mode:
            print(f"🔑 Google Sheets credentials: {google_credentials_path}")
    else:
        if debug_mode:
            print("⚠️ Google Sheets credentials not found")
    agent = Agent(
        name="Avangard Consultant",
        agent_id="avangard_consultant",
        user_id=user_id,
        session_id=session_id,
        model=OpenAIChat(id=model_id, max_tokens=5000, temperature=0.3),
        # База знаний из локальных документов
        knowledge=get_avangard_knowledge(),
        search_knowledge=True,
        enable_agentic_knowledge_filters=True,  # Автоматические фильтры поиска
        add_references=True,
        tools=[
            ExaTools(),  # интернет-поиск
            smtp_email,  # отправка email
            OpenAITools(transcription_model="whisper-1"),  # транскрипция
        ],
        show_tool_calls=True,
        add_datetime_to_instructions=True,
        timezone_identifier="Asia/Novosibirsk",
        instructions=load_consultant_instructions(),
        # Хранилище и память в Postgres
        storage=PostgresAgentStorage(table_name="avangard_consultant_sessions", db_url=db_url),
        memory=Memory(
            model=OpenAIChat(id=model_id),
            db=PostgresMemoryDb(table_name="avangard_memories", db_url=db_url),
            delete_memories=True,
            clear_memories=True,
        ),
        enable_agentic_memory=True,
        add_history_to_messages=True,
        num_history_runs=3,
        read_chat_history=True,
        markdown=False,
        debug_mode=debug_mode,
    )
    
    # Автоматическая загрузка базы знаний при инициализации
    try:
        recreate_flag = True  # сейчас всегда пересоздаём, можно вынести в env при необходимости
        if debug_mode:
            mode = "recreate=True" if recreate_flag else "recreate=False"
            print(f"🔄 Loading knowledge base ({mode})…")
            print("📁 PDF dir: agent_docs_pdf")
            print("📁 Other dir: agent_docs_other")
            # List PDF files and sizes for visibility
            pdf_dir = "agent_docs_pdf"
            if os.path.exists(pdf_dir):
                pdf_files = os.listdir(pdf_dir)
                if pdf_files:
                    print(f"📂 Files in {pdf_dir} ({len(pdf_files)}):")
                    for f in pdf_files:
                        p = os.path.join(pdf_dir, f)
                        if os.path.isfile(p):
                            try:
                                print(f"  - {f} | {os.path.getsize(p)} bytes")
                            except Exception:
                                print(f"  - {f}")
                else:
                    print(f"📂 No files in {pdf_dir}")
            else:
                print(f"❌ Directory '{pdf_dir}' not found!")

        agent.knowledge.load(recreate=recreate_flag)

        if debug_mode:
            if hasattr(agent.knowledge, 'sources'):
                print(f"✅ Combined knowledge base loaded with {len(agent.knowledge.sources)} sources")
                for source in agent.knowledge.sources:
                    source_name = getattr(source, '__class__', type(source)).__name__
                    print(f"  📋 {source_name}: Ready for search")
                    if hasattr(source, 'vector_db') and hasattr(source.vector_db, 'table_name'):
                        print(f"       Table: {source.vector_db.table_name}")
                print("📊 Knowledge base ready for search across all sources")
            else:
                print("⚠️ No sources found in combined knowledge base!")
    except Exception as e:
        if debug_mode:
            print(f"❌ Error loading knowledge base: {e}")
            import traceback
            traceback.print_exc()
    
    return agent

