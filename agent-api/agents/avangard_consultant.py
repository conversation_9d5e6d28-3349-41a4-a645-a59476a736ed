from typing import Optional
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.memory.v2.db.postgres import PostgresMemoryDb
from agno.memory.v2.memory import Memory
from agno.storage.agent.postgres import PostgresAgentStorage
from agno.knowledge.document import DocumentKnowledgeBase
from agno.knowledge.pdf import PDFKnowledgeBase, PDFReader
from agno.knowledge.combined import CombinedKnowledgeBase
from agno.vectordb.pgvector import PgVector
from agno.tools.exa import ExaTools
from agent_tools.smtp_email_tool import smtp_email
import importlib
import agent_prompts.consultant_instructions as consultant_module
from agno.tools.openai import OpenAITools
from db.session import db_url
import os


def load_consultant_instructions():
    """Горячая перезагрузка инструкций консультанта без рестарта процесса."""
    importlib.reload(consultant_module)
    text = getattr(consultant_module, 'CONSULTANT_INSTRUCTIONS', '')
    return text.split('\n\n') if isinstance(text, str) else text


def get_avangard_knowledge() -> CombinedKnowledgeBase:
    """Создает комбинированную базу знаний согласно документации Agno."""

    # PDF база знаний - нативно по документации
    pdf_knowledge_base = PDFKnowledgeBase(
        path="agent_docs_pdf",
        vector_db=PgVector(
            table_name="pdf_documents",
            db_url=db_url,
        ),
        reader=PDFReader(chunk=True),
    )

    # Документы база знаний - нативно по документации
    document_knowledge_base = DocumentKnowledgeBase(
        path="agent_docs_other",
        vector_db=PgVector(
            table_name="documents",
            db_url=db_url,
        ),
    )

    # Комбинированная база знаний - нативно по документации
    knowledge_base = CombinedKnowledgeBase(
        sources=[
            pdf_knowledge_base,
            document_knowledge_base,
        ],
        vector_db=PgVector(
            table_name="combined_documents",
            db_url=db_url,
        ),
    )

    return knowledge_base


def get_avangard_consultant(
    model_id: str = "gpt-4o-mini",
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    debug_mode: bool = True,
):
    """Создает агента Avangard Consultant согласно документации Agno."""

    # Настройка Google Sheets credentials
    google_credentials_path = os.getenv('GOOGLE_CREDENTIALS_PATH', 'meta-geography-464110-b9-55c203771d7e.json')
    if google_credentials_path and os.path.exists(google_credentials_path):
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = google_credentials_path
        if debug_mode:
            print(f"🔑 Google Sheets credentials: {google_credentials_path}")
    else:
        if debug_mode:
            print("⚠️ Google Sheets credentials not found")

    # Создаем агента согласно документации Agno
    agent = Agent(
        name="Avangard Consultant",
        agent_id="avangard_consultant",
        user_id=user_id,
        session_id=session_id,
        model=OpenAIChat(id=model_id, max_tokens=5000, temperature=0.3),
        knowledge=get_avangard_knowledge(),
        search_knowledge=True,
        tools=[
            ExaTools(),
            smtp_email,
            OpenAITools(transcription_model="whisper-1"),
        ],
        show_tool_calls=True,
        add_datetime_to_instructions=True,
        timezone_identifier="Asia/Novosibirsk",
        instructions=load_consultant_instructions(),
        storage=PostgresAgentStorage(table_name="avangard_consultant_sessions", db_url=db_url),
        memory=Memory(
            model=OpenAIChat(id=model_id),
            db=PostgresMemoryDb(table_name="avangard_memories", db_url=db_url),
        ),
        add_history_to_messages=True,
        num_history_runs=3,
        read_chat_history=True,
        markdown=False,
        debug_mode=debug_mode,
    )

    # Загружаем базу знаний согласно документации
    agent.knowledge.load(recreate=False)

    return agent

