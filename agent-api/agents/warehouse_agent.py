from typing import Optional
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.memory.v2.db.postgres import PostgresMemoryDb
from agno.memory.v2.memory import Memory
from agno.storage.agent.postgres import PostgresAgentStorage
from agno.tools.googlesheets import GoogleSheetsTools
import importlib
import agent_prompts.warehouse_instructions as warehouse_module
from db.session import db_url
import os


def load_warehouse_instructions():
    """Горячая перезагрузка инструкций складского агента без рестарта процесса.
    Возвращает список инструкций, совместимый с Agent(..., instructions=...)
    """
    importlib.reload(warehouse_module)
    text = getattr(warehouse_module, 'WAREHOUSE_INSTRUCTIONS', '')
    return text.split('\n\n') if isinstance(text, str) else text

def get_warehouse_agent(
    model_id: str = "gpt-4.1",
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    debug_mode: bool = True,
):
    """Складской агент для работы с Google Sheets."""
    
    # Настройка Google Sheets credentials
    google_credentials_path = os.getenv('GOOGLE_CREDENTIALS_PATH', 'meta-geography-464110-b9-55c203771d7e.json')
    if google_credentials_path and os.path.exists(google_credentials_path):
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = google_credentials_path
        if debug_mode:
            print(f"🔑 Google Sheets credentials: {google_credentials_path}")
    else:
        if debug_mode:
            print("⚠️ Google Sheets credentials not found")
    
    agent = Agent(
        name="Warehouse Manager",
        agent_id="warehouse_manager",
        user_id=user_id,
        session_id=session_id,
        model=OpenAIChat(id=model_id, max_completion_tokens=5000, temperature=0.3),
        tools=[
            GoogleSheetsTools(
                spreadsheet_id=os.getenv("GOOGLE_SPREADSHEET_ID", "1Mqak3XJtkwC-UJ1ZGihGNm28trUwpVQfX3EKpY9iMHo"),
                spreadsheet_range="A1:F100",  # Диапазон по умолчанию
                read=True,      # Чтение данных
                update=True,    # Обновление данных
                create=True,    # Создание новых листов
                duplicate=True  # Дублирование листов
            ),
        ],
        show_tool_calls=True,
        add_datetime_to_instructions=True,
        timezone_identifier="Asia/Novosibirsk",
        instructions=load_warehouse_instructions(),
        # Хранилище и память в Postgres
        storage=PostgresAgentStorage(table_name="warehouse_agent_sessions", db_url=db_url),
        memory=Memory(
            model=OpenAIChat(id=model_id),
            db=PostgresMemoryDb(table_name="warehouse_memories", db_url=db_url),
            delete_memories=True,
            clear_memories=True,
        ),
        enable_agentic_memory=True,
        add_history_to_messages=True,
        num_history_runs=3,
        read_chat_history=True,
        markdown=True,
        debug_mode=debug_mode,
    )
    
    if debug_mode:
        print("🏭 Warehouse Agent initialized")
        print(f"📊 Spreadsheet ID: {os.getenv('GOOGLE_SPREADSHEET_ID')}")
        print("📋 Available sheets: 8 sheets ready for operations")
    
    return agent