from enum import Enum
from typing import List, Optional

# from agents.agno_assist import get_agno_assist  # Временно отключен
# from agents.finance_agent import get_finance_agent  # Временно отключен
# from agents.web_agent import get_web_agent  # Временно отключен
from agents.avangard_consultant import get_avangard_consultant
from agents.warehouse_agent import get_warehouse_agent


class AgentType(Enum):
    # WEB_AGENT = "web_agent"  # Временно отключен
    # AGNO_ASSIST = "agno_assist"  # Временно отключен
    # FINANCE_AGENT = "finance_agent"  # Временно отключен
    AVANGARD_CONSULTANT = "avangard_consultant"
    WAREHOUSE_AGENT = "warehouse_agent"


def get_available_agents() -> List[str]:
    """Returns a list of all available agent IDs."""
    return [agent.value for agent in AgentType]


def get_agent(
    model_id: str = "gpt-4.1",
    agent_id: Optional[AgentType] = None,
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    debug_mode: bool = True,
):
    # if agent_id == AgentType.WEB_AGENT:  # Временно отключен
    #     return get_web_agent(model_id=model_id, user_id=user_id, session_id=session_id, debug_mode=debug_mode)
    # elif agent_id == AgentType.AGNO_ASSIST:  # Временно отключен
    #     return get_agno_assist(model_id=model_id, user_id=user_id, session_id=session_id, debug_mode=debug_mode)
    # elif agent_id == AgentType.FINANCE_AGENT:  # Временно отключен
    #     return get_finance_agent(model_id=model_id, user_id=user_id, session_id=session_id, debug_mode=debug_mode)
    if agent_id == AgentType.AVANGARD_CONSULTANT:
        return get_avangard_consultant(model_id=model_id, user_id=user_id, session_id=session_id, debug_mode=debug_mode)
    elif agent_id == AgentType.WAREHOUSE_AGENT:
        return get_warehouse_agent(model_id=model_id, user_id=user_id, session_id=session_id, debug_mode=debug_mode)

    raise ValueError(f"Agent: {agent_id} not found")
