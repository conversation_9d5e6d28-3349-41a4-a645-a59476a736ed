# Вопрос о компании (будет искать в базе знаний)
curl -X POST "http://localhost:8000/v1/agents/avangard_consultant/runs" \
  -H "Content-Type: application/json" \
  -d '{"message": "Что ты знаешь о компании Авангард?", "stream": false}'

# Вопрос о запчастях (будет искать в Google Sheets)
curl -X POST "http://localhost:8000/v1/agents/avangard_consultant/runs" \
  -H "Content-Type: application/json" \
  -d '{"message": "Какие запчасти есть в наличии?", "stream": false}'

# Общий вопрос (будет использовать экспертность)
curl -X POST "http://localhost:8000/v1/agents/avangard_consultant/runs" \
  -H "Content-Type: application/json" \
  -d '{"message": "Привет! Расскажи о своих возможностях", "stream": false}'



source .venv/bin/activate 

python scripts/....py

uvicorn api.main:app --reload --host 0.0.0.0 --port 8000

/var/www/devcode_su_usr94/data/www/devcode.su/avangard_ai/back/api/telegram_bot_v2.py









