from textwrap import dedent
from typing import Optional

from agno.agent import Agent
from agno.memory.v2.db.postgres import PostgresMemoryDb
from agno.memory.v2.memory import Memory
from agno.models.openai import OpenAIChat
from agno.storage.agent.postgres import PostgresAgentStorage
from agno.tools.openai import OpenAITools

from db.session import db_url


def get_avangard_consultant(
    model_id: str = "gpt-4o-mini",
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    debug_mode: bool = True,
) -> Agent:
    
    return Agent(
        name="Avangard Consultant",
        agent_id="avangard_consultant",
        user_id=user_id,
        session_id=session_id,
        model=OpenAIChat(id=model_id, max_tokens=2000, temperature=0.3),
        
        tools=[
            OpenAITools(transcription_model="whisper-1"),
        ],
        show_tool_calls=True,
        
        description="Консультант Авангард (локальный режим, Postgres-хранилище и память)",
        
        instructions=dedent("""\
            Ты консультант Новосибирского кранового завода "Авангард".

            ⚠️ Локальный режим: поиск в Google Sheets отключён. Отвечай, используя контекст диалога и внутреннюю память.

            БАЗОВАЯ ИНФОРМАЦИЯ (отвечай БЕЗ поиска):
            - Новосибирский крановый завод "Авангард"
            - Производство, монтаж, обслуживание грузоподъемного оборудования
            - Краны, тали, лебедки, запчасти
            - Работаем более 20 лет в Новосибирске

            При необходимости уточняй у пользователя детали запроса и сохраняй важный контекст в память.

            ДОСТУПНЫЕ ИНСТРУМЕНТЫ (только для email):
            - smtp_email - отправка писем
            - OpenAITools - транскрипция аудио

            Для поиска запчастей внешние источники временно недоступны.
        """),

        
        storage=PostgresAgentStorage(table_name="avangard_consultant_sessions", db_url=db_url),
        memory=Memory(model=OpenAIChat(id=model_id), db=PostgresMemoryDb(table_name="avangard_memories", db_url=db_url)),
        
        debug_mode=debug_mode,
    )