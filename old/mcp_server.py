#!/usr/bin/env python3
"""
ОПТИМИЗИРОВАННЫЙ Python MCP Server для работы с Google Sheets
Основные улучшения:
- Кэширование структуры листов
- Batch операции
- Умное управление rate limit
- Сокращение количества API вызовов
- УНИВЕРСАЛЬНЫЙ fuzzy поиск с rapidfuzz
"""

import json
import os
import logging
import time
import re
from typing import Any, Dict, Optional, List, Tuple
from datetime import datetime, timedelta
from functools import lru_cache
from dotenv import load_dotenv

from mcp.server.fastmcp import FastMCP

# Google Sheets API
from googleapiclient.discovery import build
from google.oauth2.service_account import Credentials as ServiceAccountCredentials

# Fuzzy поиск
from rapidfuzz import fuzz, process

# Загружаем переменные окружения
load_dotenv()

# Настройка логирования
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mcp_server.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Создаем FastMCP сервер
mcp = FastMCP("Google Sheets MCP Server - Optimized")

# Глобальные переменные для оптимизации
sheets_service = None
sheets_structure_cache = {}  # Кэш структуры листов
cache_timestamps = {}  # Временные метки кэша
CACHE_TIMEOUT = 300  # 5 минут
RATE_LIMIT_DELAY = 0.2  # 200ms между запросами

class SheetsCache:
    """Умный кэш для Google Sheets данных"""
    def __init__(self, timeout_seconds=300):
        self.cache = {}
        self.timestamps = {}
        self.timeout = timeout_seconds
    
    def get(self, key):
        if key in self.cache:
            # Проверяем не истек ли кэш
            if datetime.now() - self.timestamps[key] < timedelta(seconds=self.timeout):
                logger.info(f"📦 Данные из кэша: {key}")
                return self.cache[key]
            else:
                # Удаляем устаревший кэш
                del self.cache[key]
                del self.timestamps[key]
        return None
    
    def set(self, key, value):
        self.cache[key] = value
        self.timestamps[key] = datetime.now()
        logger.info(f"💾 Данные сохранены в кэш: {key}")
    
    def clear(self):
        self.cache.clear()
        self.timestamps.clear()

# Глобальный кэш
global_cache = SheetsCache(CACHE_TIMEOUT)

def universal_fuzzy_search(query: str, cell_value: str, threshold: int = 70) -> Tuple[bool, int, str]:
    """УНИВЕРСАЛЬНЫЙ fuzzy поиск - работает с любыми товарами
    
    Args:
        query: Поисковый запрос 
        cell_value: Значение из ячейки
        threshold: Минимальный score для совпадения (0-100)
        
    Returns:
        (found, score, match_type)
    """
    if not query or not cell_value:
        return False, 0, "empty"
    
    query_clean = re.sub(r'[^\w\s]', ' ', query.lower()).strip()
    cell_clean = re.sub(r'[^\w\s]', ' ', str(cell_value).lower()).strip()
    
    # 1. Точное совпадение
    if query_clean == cell_clean:
        return True, 100, "exact"
    
    # 2. Простой substring (быстрый)
    if query_clean in cell_clean:
        return True, 95, "substring"
    
    # 3. Fuzzy ratio (полное сравнение)
    ratio_score = fuzz.ratio(query_clean, cell_clean)
    if ratio_score >= threshold:
        return True, ratio_score, "fuzzy_ratio"
    
    # 4. Partial ratio (частичное совпадение) 
    partial_score = fuzz.partial_ratio(query_clean, cell_clean)
    if partial_score >= threshold:
        return True, partial_score, "partial_ratio"
    
    # 5. Token sort ratio (игнорирует порядок слов)
    token_score = fuzz.token_sort_ratio(query_clean, cell_clean)
    if token_score >= threshold:
        return True, token_score, "token_sort"
    
    # 6. Token set ratio (игнорирует дубликаты и порядок)
    token_set_score = fuzz.token_set_ratio(query_clean, cell_clean)
    if token_set_score >= threshold:
        return True, token_set_score, "token_set"
    
    return False, max(ratio_score, partial_score, token_score, token_set_score), "no_match"

def search_in_row_data(query: str, row_data: List[str]) -> Tuple[bool, int, Dict]:
    """Поиск в данных строки с универсальным fuzzy поиском"""
    best_match = {"found": False, "score": 0, "column": -1, "text": "", "type": ""}
    
    # Ищем в первых 3 колонках
    for col_idx, cell_value in enumerate(row_data[:3]):
        if not cell_value:
            continue
            
        found, score, match_type = universal_fuzzy_search(query, str(cell_value))
        
        # Берем лучшее совпадение
        if found and score > best_match["score"]:
            best_match = {
                "found": True,
                "score": score,
                "column": col_idx,
                "text": str(cell_value),
                "type": match_type
            }
    
    if best_match["found"]:
        return True, best_match["column"], {
            "found_text": best_match["text"],
            "score": best_match["score"], 
            "match_type": best_match["type"],
            "column": chr(65 + best_match["column"])
        }
    
    return False, -1, {}

async def init_google_sheets():
    """Инициализация Google Sheets API (с кэшированием)"""
    global sheets_service

    if sheets_service:
        return sheets_service

    try:
        credentials_path = os.getenv('GOOGLE_CREDENTIALS_PATH')
        logger.info(f"🔑 Инициализация Google Sheets API: {credentials_path}")

        if not credentials_path or not os.path.exists(credentials_path):
            error_msg = f"Файл учетных данных не найден: {credentials_path}"
            logger.error(error_msg)
            raise ValueError(error_msg)

        credentials = ServiceAccountCredentials.from_service_account_file(
            credentials_path,
            scopes=['https://www.googleapis.com/auth/spreadsheets']
        )
        
        sheets_service = build('sheets', 'v4', credentials=credentials)
        logger.info("✅ Google Sheets API инициализирован")
        return sheets_service

    except Exception as e:
        logger.error(f"❌ Ошибка инициализации Google Sheets API: {e}")
        raise

def rate_limit_delay():
    """Умная задержка для избежания rate limit"""
    time.sleep(RATE_LIMIT_DELAY)

def retry_with_backoff(func, max_retries=3):
    """Повторные попытки с экспоненциальной задержкой"""
    for attempt in range(max_retries):
        try:
            return func()
        except Exception as e:
            if "429" in str(e) or "Quota exceeded" in str(e) or "Rate Limit Exceeded" in str(e):
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) * 1  # 1, 2, 4 секунды
                    logger.warning(f"⏳ Rate limit, ожидание {wait_time}с (попытка {attempt + 1}/{max_retries})")
                    time.sleep(wait_time)
                    continue
            raise e
    raise Exception(f"Не удалось выполнить операцию после {max_retries} попыток")

@mcp.tool()
async def get_all_sheets_structure_cached(spreadsheet_id: str = None) -> str:
    """ОПТИМИЗИРОВАННОЕ получение структуры листов с кэшированием"""
    try:
        if not spreadsheet_id:
            spreadsheet_id = os.getenv("GOOGLE_SPREADSHEET_ID")
            if not spreadsheet_id:
                return json.dumps({'error': 'ID таблицы не указан'}, ensure_ascii=False)

        cache_key = f"structure_{spreadsheet_id}"
        
        # Проверяем кэш
        cached_data = global_cache.get(cache_key)
        if cached_data:
            return cached_data

        service = await init_google_sheets()
        logger.info(f"🏗️ Получение структуры листов (НЕ из кэша): {spreadsheet_id}")

        # Получаем список листов ОДНИМ запросом
        def get_spreadsheet():
            return service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()
        
        spreadsheet = retry_with_backoff(get_spreadsheet)
        
        sheets_structure = []
        
        # Batch запрос для получения заголовков всех листов
        ranges_to_read = []
        sheet_titles = []
        
        for sheet in spreadsheet.get('sheets', []):
            sheet_title = sheet['properties']['title']
            # Пропускаем служебные листы для оптимизации
            if not any(skip in sheet_title.lower() for skip in ['temp', 'test', 'архив', 'backup', 'права']):
                ranges_to_read.append(f"'{sheet_title}'!1:1")
                sheet_titles.append(sheet['properties'])

        # Batch запрос для получения заголовков
        if ranges_to_read:
            def batch_get_headers():
                return service.spreadsheets().values().batchGet(
                    spreadsheetId=spreadsheet_id,
                    ranges=ranges_to_read
                ).execute()
            
            rate_limit_delay()  # Задержка перед batch запросом
            batch_result = retry_with_backoff(batch_get_headers)
            
            for idx, value_range in enumerate(batch_result.get('valueRanges', [])):
                sheet_props = sheet_titles[idx]
                headers = value_range.get('values', [[]])[0] if value_range.get('values') else []
                
                # Создаем mapping колонок
                column_mapping = {}
                quantity_column = None
                
                for col_idx, header in enumerate(headers):
                    if col_idx < 26:  # Ограничиваем A-Z
                        column_letter = chr(65 + col_idx)
                        column_mapping[column_letter] = header
                        
                        # Ищем колонку с количеством
                        header_lower = str(header).lower()
                        if any(word in header_lower for word in ['количество', 'кол-во', 'кол', 'qty']):
                            quantity_column = column_letter
                
                sheets_structure.append({
                    'sheet_title': sheet_props['title'],
                    'sheet_id': sheet_props['sheetId'],
                    'headers': headers,
                    'column_mapping': column_mapping,
                    'quantity_column': quantity_column,
                    'total_columns': len(headers)
                })

        result = json.dumps({
            "spreadsheet_title": spreadsheet.get('properties', {}).get('title'),
            "spreadsheet_id": spreadsheet_id,
            "total_sheets": len(sheets_structure),
            "sheets_structure": sheets_structure,
            "cached": False,
            "timestamp": datetime.now().isoformat()
        }, ensure_ascii=False, indent=2)

        # Сохраняем в кэш
        global_cache.set(cache_key, result)
        
        logger.info(f"✅ Структура {len(sheets_structure)} листов получена и закэширована")
        return result

    except Exception as e:
        logger.error(f"❌ Ошибка получения структуры листов: {e}")
        return f"Ошибка получения структуры листов: {str(e)}"

@mcp.tool()
async def search_product_universal(product_name: str, spreadsheet_id: str = None, limit_sheets: bool = True) -> str:
    """UNIVERSAL fuzzy поиск товара - находит всё, даже с опечатками"""
    try:
        if not spreadsheet_id:
            spreadsheet_id = os.getenv("GOOGLE_SPREADSHEET_ID")
            if not spreadsheet_id:
                return json.dumps({'error': 'ID таблицы не указан'}, ensure_ascii=False)

        # Проверяем кэш поиска
        cache_key = f"universal_search_{spreadsheet_id}_{product_name.lower()}"
        cached_result = global_cache.get(cache_key)
        if cached_result:
            logger.info(f"🎯 Универсальный поиск из кэша для '{product_name}'")
            return cached_result

        service = await init_google_sheets()
        logger.info(f"🔍 УНИВЕРСАЛЬНЫЙ FUZZY поиск: '{product_name}'")

        # Получаем структуру из кэша или загружаем
        structure_result = await get_all_sheets_structure_cached(spreadsheet_id)
        structure_data = json.loads(structure_result)
        
        found_products = []
        
        # Приоритетные листы (как было)
        priority_sheets = []
        if limit_sheets:
            for sheet_info in structure_data.get('sheets_structure', []):
                sheet_title = sheet_info['sheet_title']
                if any(keyword in sheet_title.lower() for keyword in ['запчасти', 'концевые', 'комплектующие', 'муфты', 'шестерни', 'подшипники']):
                    priority_sheets.append(sheet_title)
                    if len(priority_sheets) >= 10:
                        break
        else:
            priority_sheets = [s['sheet_title'] for s in structure_data.get('sheets_structure', [])]

        logger.info(f"📋 Универсальный поиск в {len(priority_sheets)} листах")
        
        # Batch поиск по приоритетным листам
        search_ranges = [f"'{sheet}'!A1:J300" for sheet in priority_sheets]
        
        if search_ranges:
            def batch_search():
                return service.spreadsheets().values().batchGet(
                    spreadsheetId=spreadsheet_id,
                    ranges=search_ranges
                ).execute()
            
            rate_limit_delay()
            batch_result = retry_with_backoff(batch_search)
            
            for idx, value_range in enumerate(batch_result.get('valueRanges', [])):
                sheet_title = priority_sheets[idx]
                values = value_range.get('values', [])
                
                if not values:
                    continue
                    
                headers = values[0] if values else []
                
                # УНИВЕРСАЛЬНЫЙ fuzzy поиск по строкам
                for row_idx, row in enumerate(values[1:], start=2):
                    if not row:
                        continue
                    
                    found, col_idx, match_info = search_in_row_data(product_name, row)
                    if found:
                        # Нашли совпадение с универсальным поиском
                        product_info = {
                            "sheet": sheet_title,
                            "row": row_idx,
                            "found_in_column": match_info['column'],
                            "found_text": match_info['found_text'],
                            "fuzzy_score": match_info['score'],
                            "match_type": match_info['match_type'],
                            "full_row_data": {}
                        }
                        
                        # Собираем данные строки
                        for i, header in enumerate(headers):
                            if i < len(row) and row[i]:
                                product_info["full_row_data"][header] = row[i]
                                
                        found_products.append(product_info)

        # Сортируем по fuzzy score (лучшие сначала)
        found_products.sort(key=lambda x: x.get('fuzzy_score', 0), reverse=True)

        result = json.dumps({
            "search_query": product_name,
            "search_type": "universal_fuzzy",
            "spreadsheet_id": spreadsheet_id,
            "total_found": len(found_products),
            "found_products": found_products,
            "library_used": "rapidfuzz",
            "features": ["Опечатки", "Частичные совпадения", 
                        "Разный порядок слов", "Сокращения",
                        "Без харкода словарей"]
        }, ensure_ascii=False, indent=2)

        # Кэшируем результат поиска на 2 минуты 
        search_cache = SheetsCache(120)
        search_cache.set(cache_key, result)

        logger.info(f"✅ Универсальный fuzzy поиск нашел {len(found_products)} товаров")
        return result

    except Exception as e:
        logger.error(f"❌ Ошибка универсального поиска: {e}")
        return f"Ошибка универсального поиска: {str(e)}"

@mcp.tool()
async def search_product_optimized(product_name: str, spreadsheet_id: str = None, limit_sheets: bool = True) -> str:
    """Алиас для универсального поиска - замена старой функции"""
    return await search_product_universal(product_name, spreadsheet_id, limit_sheets)

@mcp.tool()
async def batch_update_products(spreadsheet_id: str, updates: list) -> str:
    """Batch обновление нескольких товаров одним запросом"""
    try:
        service = await init_google_sheets()
        logger.info(f"🔄 Batch обновление {len(updates)} позиций")

        # Формируем batch запрос
        requests = []
        for update in updates:
            sheet_name = update['sheet_name']
            row = update['row']
            column = update['column']
            value = update['value']
            
            requests.append({
                'updateCells': {
                    'range': {
                        'sheetId': update.get('sheet_id'),
                        'startRowIndex': row - 1,
                        'endRowIndex': row,
                        'startColumnIndex': ord(column) - ord('A'),
                        'endColumnIndex': ord(column) - ord('A') + 1
                    },
                    'rows': [{
                        'values': [{
                            'userEnteredValue': {'stringValue': str(value)}
                        }]
                    }],
                    'fields': 'userEnteredValue'
                }
            })

        # Выполняем batch update
        def batch_update():
            return service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body={'requests': requests}
            ).execute()

        result = retry_with_backoff(batch_update)
        
        # Очищаем кэш после изменений
        global_cache.clear()
        
        logger.info(f"✅ Batch обновление завершено: {len(updates)} позиций")
        
        return json.dumps({
            "updated_count": len(updates),
            "updates": updates,
            "cache_cleared": True,
            "timestamp": datetime.now().isoformat()
        }, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ Ошибка batch обновления: {e}")
        return f"Ошибка batch обновления: {str(e)}"

# Переопределяем оригинальные функции с оптимизацией
@mcp.tool()
async def get_all_sheets_structure(spreadsheet_id: str = None) -> str:
    """Алиас для кэшированной версии"""
    return await get_all_sheets_structure_cached(spreadsheet_id)

@mcp.tool() 
async def search_product(product_name: str, spreadsheet_id: str = None) -> str:
    """Алиас для оптимизированного поиска"""
    return await search_product_optimized(product_name, spreadsheet_id)

# Оригинальные функции остаются для совместимости
@mcp.tool()
async def read_sheet(spreadsheet_id: str, range: str) -> str:
    """Читает данные из Google Sheets (с кэшированием)"""
    try:
        cache_key = f"read_{spreadsheet_id}_{range}"
        cached_data = global_cache.get(cache_key)
        if cached_data:
            return cached_data

        service = await init_google_sheets()
        logger.info(f"📖 Чтение: {range}")

        def read_data():
            return service.spreadsheets().values().get(
                spreadsheetId=spreadsheet_id,
                range=range
            ).execute()

        rate_limit_delay()
        result = retry_with_backoff(read_data)
        values = result.get('values', [])

        if values:
            headers = values[0] if values else []
            data_rows = values[1:] if len(values) > 1 else []
            
            structured_data = []
            for row_idx, row in enumerate(data_rows, start=2):
                row_data = {
                    "row_number": row_idx,
                    "data": {}
                }
                
                for col_idx, header in enumerate(headers):
                    value = row[col_idx] if col_idx < len(row) else ""
                    row_data["data"][header] = value
                    
                structured_data.append(row_data)
            
            response = json.dumps({
                "range": range,
                "headers": headers,
                "raw_values": values,
                "structured_data": structured_data,
                "total_rows": len(values),
                "cached": False
            }, ensure_ascii=False, indent=2)
        else:
            response = json.dumps({
                "range": range,
                "headers": [],
                "raw_values": [],
                "structured_data": [],
                "total_rows": 0,
                "cached": False
            }, ensure_ascii=False, indent=2)

        # Кэшируем на 1 минуту
        read_cache = SheetsCache(60)
        read_cache.set(cache_key, response)
        
        return response

    except Exception as e:
        logger.error(f"❌ Ошибка чтения: {e}")
        return f"Ошибка чтения данных: {str(e)}"

@mcp.tool()
async def write_sheet(spreadsheet_id: str, range: str, values: list[list[str]]) -> str:
    """Записывает данные в Google Sheets (с очисткой кэша)"""
    try:
        service = await init_google_sheets()
        logger.info(f"✏️ Запись в: {range}")

        body = {'values': values}

        def write_data():
            return service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=range,
                valueInputOption='RAW',
                body=body
            ).execute()

        rate_limit_delay()
        result = retry_with_backoff(write_data)

        # Очищаем кэш после записи
        global_cache.clear()
        
        logger.info(f"✅ Записано ячеек: {result.get('updatedCells')}")

        return json.dumps({
            "updated_range": result.get('updatedRange'),
            "updated_rows": result.get('updatedRows'),
            "updated_columns": result.get('updatedColumns'),
            "updated_cells": result.get('updatedCells'),
            "cache_cleared": True
        }, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ Ошибка записи: {e}")
        return f"Ошибка записи данных: {str(e)}"

@mcp.tool()
async def create_sheet(spreadsheet_id: str, sheet_title: str) -> str:
    """Создает новый лист в таблице"""
    try:
        service = await init_google_sheets()
        logger.info(f"➕ Создание листа: {sheet_title}")

        requests = [{
            'addSheet': {
                'properties': {
                    'title': sheet_title
                }
            }
        }]

        body = {'requests': requests}

        def create_new_sheet():
            return service.spreadsheets().batchUpdate(
                spreadsheetId=spreadsheet_id,
                body=body
            ).execute()

        result = retry_with_backoff(create_new_sheet)
        new_sheet_id = result['replies'][0]['addSheet']['properties']['sheetId']
        
        # Очищаем кэш структуры
        global_cache.clear()
        
        logger.info(f"✅ Лист создан с ID: {new_sheet_id}")

        return json.dumps({
            "sheet_title": sheet_title,
            "sheet_id": new_sheet_id,
            "message": f"Лист '{sheet_title}' успешно создан"
        }, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ Ошибка создания листа: {e}")
        return f"Ошибка создания листа: {str(e)}"

@mcp.tool()
async def list_sheets(spreadsheet_id: str = None) -> str:
    """Получает список всех листов в таблице (с кэшированием)"""
    try:
        if not spreadsheet_id:
            spreadsheet_id = os.getenv("GOOGLE_SPREADSHEET_ID")
            if not spreadsheet_id:
                return json.dumps({'error': 'ID таблицы не указан'}, ensure_ascii=False)

        cache_key = f"list_{spreadsheet_id}"
        cached_data = global_cache.get(cache_key)
        if cached_data:
            return cached_data

        service = await init_google_sheets()
        logger.info(f"📋 Список листов: {spreadsheet_id}")

        def get_sheets():
            return service.spreadsheets().get(spreadsheetId=spreadsheet_id).execute()

        result = retry_with_backoff(get_sheets)

        sheets = []
        for sheet in result.get('sheets', []):
            properties = sheet.get('properties', {})
            sheets.append({
                'title': properties.get('title'),
                'sheetId': properties.get('sheetId'),
                'index': properties.get('index')
            })

        response = json.dumps({
            "spreadsheet_title": result.get('properties', {}).get('title'),
            "sheets": sheets,
            "total_sheets": len(sheets),
            "cached": False
        }, ensure_ascii=False, indent=2)

        global_cache.set(cache_key, response)
        
        logger.info(f"✅ Найдено {len(sheets)} листов")
        return response

    except Exception as e:
        logger.error(f"❌ Ошибка получения списка листов: {e}")
        return f"Ошибка получения списка листов: {str(e)}"

@mcp.tool()
async def update_product_quantity(spreadsheet_id: str, sheet_name: str, row: int, quantity_column: str, new_quantity: str) -> str:
    """Обновить количество товара в конкретной ячейке"""
    try:
        service = await init_google_sheets()
        logger.info(f"🔢 Обновление количества: {sheet_name}!{quantity_column}{row} = {new_quantity}")

        range_to_update = f"{sheet_name}!{quantity_column}{row}"
        body = {'values': [[new_quantity]]}

        def update_quantity():
            return service.spreadsheets().values().update(
                spreadsheetId=spreadsheet_id,
                range=range_to_update,
                valueInputOption='RAW',
                body=body
            ).execute()

        rate_limit_delay()
        result = retry_with_backoff(update_quantity)

        # Очищаем кэш после изменения
        global_cache.clear()
        
        logger.info(f"✅ Количество обновлено: {range_to_update}")

        return json.dumps({
            "updated_range": range_to_update,
            "new_quantity": new_quantity,
            "updated_cells": result.get('updatedCells'),
            "message": f"Количество товара обновлено на {new_quantity}",
            "cache_cleared": True
        }, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ Ошибка обновления количества: {e}")
        return f"Ошибка обновления количества: {str(e)}"

def main():
    """Запуск оптимизированного FastMCP сервера"""
    import sys
    print("� Запусук ОПТИМИЗИРОВАННОГО Google Sheets MCP сервера...", file=sys.stderr)
    print("📋 Инструменты: read_sheet, write_sheet, create_sheet, list_sheets, search_product, update_product_quantity, batch_update_products", file=sys.stderr)
    print("⚡ ОПТИМИЗАЦИИ: кэширование, batch операции, умный rate limit", file=sys.stderr)
    print("🎯 FUZZY ПОИСК: rapidfuzz, опечатки, частичные совпадения, разный порядок слов", file=sys.stderr)
    
    credentials_path = os.getenv('GOOGLE_CREDENTIALS_PATH')
    if not credentials_path or not os.path.exists(credentials_path):
        print("❌ ОШИБКА: Файл учетных данных не найден", file=sys.stderr)
        exit(1)
    
    print(f"✅ Файл учетных данных: {credentials_path}", file=sys.stderr)
    print("📡 Сервер готов к работе через STDIO", file=sys.stderr)
    print("💾 Кэширование активно", file=sys.stderr)
    print("=" * 50, file=sys.stderr)

    try:
        logger.info("ОПТИМИЗИРОВАННЫЙ FastMCP сервер запущен через STDIO")
        mcp.run()
    except KeyboardInterrupt:
        print("\n👋 Сервер остановлен", file=sys.stderr)
        logger.info("Сервер остановлен пользователем")
    except Exception as e:
        print(f"\n❌ ОШИБКА сервера: {e}", file=sys.stderr)
        logger.error(f"Критическая ошибка сервера: {e}")
        raise

if __name__ == "__main__":
    import sys
    main()