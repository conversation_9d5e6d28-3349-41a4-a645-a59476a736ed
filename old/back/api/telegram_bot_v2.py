import os
import asyncio
import logging
from typing import Optional

from aiogram import <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, types
from aiogram.filters import Command
from aiogram.types import Message, InlineKeyboardMarkup, InlineKeyboardButton, CallbackQuery
from aiogram.webhook.aiohttp_server import SimpleRequestHandler, setup_application
from aiohttp import web
import aiohttp
 
# Подхватываем переменные окружения из .env при локальном запуске
try:
    from dotenv import load_dotenv  # type: ignore
    load_dotenv()
except Exception:
    pass

# В этом боте НЕ создаем агента локально — общаемся с существующим Agent API

# Конфигурация
ADMIN_IDS = [1828473337]  # Telegram ID администраторов

# Настройка логирования
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def markdown_to_html(text: str) -> str:
    """Конвертирует Markdown в HTML для Telegram"""
    import re
    # **текст** -> <b>текст</b> (СНАЧАЛА двойные звездочки)
    text = re.sub(r'\*\*([^*]+)\*\*', r'<b>\1</b>', text)
    # *текст* -> <b>текст</b> (ПОТОМ одинарные звездочки)
    text = re.sub(r'\*([^*]+)\*', r'<b>\1</b>', text)
    # _текст_ -> <i>текст</i>
    text = re.sub(r'_([^_]+)_', r'<i>\1</i>', text)
    return text


class AvangardTelegramBot:
    """Современный Telegram бот для работы с Avangard Consultant через FastAPI архитектуру"""
    
    def __init__(self, token: str):
        self.bot = Bot(token=token)
        self.dp = Dispatcher()
        # Настройки API, к которому шлём сообщения
        self.api_base_url = os.getenv("AGENT_API_URL", "http://localhost:8000")
        self.agent_id = os.getenv("AGENT_ID", "avangard_consultant")
        # Больше НЕ создаём локальных агентов и не трогаем БД
        self.setup_handlers()
    
    def is_admin(self, user_id: int) -> bool:
        """Проверяет, является ли пользователь администратором"""
        return user_id in ADMIN_IDS
    
    async def call_agent_api(self, user_id: int, text: str) -> str:
        """Отправляет сообщение в существующий Agent API и возвращает ответ."""
        url = f"{self.api_base_url}/v1/agents/{self.agent_id}/runs"
        payload = {
            "message": text,
            "stream": False,
            "session_id": f"telegram_{user_id}",
            "user_id": str(user_id),
        }
        timeout = aiohttp.ClientTimeout(total=60)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(url, json=payload) as resp:
                # В нашем API non-stream ответ — это просто текст
                text_resp = await resp.text()
                return text_resp
    
    def setup_handlers(self):
        """Настройка обработчиков сообщений"""
        
        @self.dp.message(Command("start"))
        async def start_handler(message: Message):
            """Обработчик команды /start"""
            user_name = message.from_user.first_name or "Пользователь"
            welcome_text = self.get_welcome_message(user_name)
            
            await message.answer(
                welcome_text,
                parse_mode="HTML"
            )
        
        @self.dp.message(Command("help"))
        async def help_handler(message: Message):
            """Справка по командам"""
            help_text = """
<b>🤖 Avangard Consultant Bot</b>

<b>Основные команды:</b>
/start - Начать работу с ботом
/help - Показать эту справку
/status - Информация о боте

<b>Возможности:</b>
• 💬 Текстовые сообщения
• 🎤 Голосовые сообщения (транскрипция)
• 📄 Работа с документами
• 🔍 Поиск в базе знаний
• 📧 Отправка email
• 🌐 Поиск в интернете

Просто напишите сообщение или отправьте голосовое!
            """
            await message.answer(help_text, parse_mode="HTML")
        
        @self.dp.message(Command("status"))
        async def status_handler(message: Message):
            """Показать статус бота"""
            user_id = message.from_user.id
            is_admin = self.is_admin(user_id)
            
            status_text = f"""
<b>📊 Статус бота</b>

<b>Пользователь:</b> {message.from_user.first_name} ({user_id})
<b>Права:</b> {'👑 Администратор' if is_admin else '👤 Пользователь'}
<b>Агент:</b> Avangard Consultant
<b>Модель:</b> GPT-4o Mini
<b>Статус:</b> ✅ Активен
            """
            await message.answer(status_text, parse_mode="HTML")
        

        
        @self.dp.message()
        async def text_handler(message: Message):
            """Обработчик текстовых и голосовых сообщений"""
            if message.text:
                # Текстовое сообщение
                await self.process_user_message(message, message.text)
            elif message.voice:
                # Голосовое сообщение -> скачиваем и транскрибируем через OpenAI Whisper
                await message.answer("🎤 Обрабатываю голосовое сообщение...")
                voice_file = None
                try:
                    voice_file = await self.download_voice_file(message.voice.file_id)
                    if not voice_file:
                        await message.answer("❌ Не удалось скачать голосовое сообщение")
                        return
                    transcript = await self.transcribe_voice_file(voice_file)
                    if transcript:
                        await message.answer(f"🎤 Распознано: <i>{transcript}</i>", parse_mode="HTML")
                        await self.process_user_message(message, transcript)
                    else:
                        await message.answer("❌ Не удалось распознать речь")
                except Exception as e:
                    logger.error(f"Ошибка обработки голосового сообщения: {e}")
                    await message.answer("❌ Произошла ошибка при обработке голосового сообщения")
                finally:
                    if voice_file and os.path.exists(voice_file):
                        os.remove(voice_file)
            elif message.audio:
                # Аудиофайл - обрабатываем как голосовое
                await message.answer("🎵 Обрабатываю аудиофайл...")
                # Здесь можно добавить обработку аудиофайлов если нужно
    
    async def process_user_message(self, message: Message, text: str):
        """Обработка сообщения пользователя через агента"""
        user_id = message.from_user.id
        user_name = message.from_user.first_name or "Пользователь"
        
        try:
            # Показываем индикатор печати
            await self.bot.send_chat_action(message.chat.id, "typing")
            
            # Отправляем сообщение в существующий Agent API
            logger.info(f"👤 {user_name} ({user_id}): {text}")
            
            response_text = await self.call_agent_api(user_id, text)

            # Санитизация Markdown-артефактов перед отправкой как plain text
            response_text = self.sanitize_plain_text_for_telegram(response_text)

            # Отправляем ответ как обычный текст (без Markdown/HTML)
            await self.send_long_message(message.chat.id, response_text)
            
            logger.info(f"🤖 Ответ отправлен пользователю {user_id}")
            
        except Exception as e:
            logger.error(f"Ошибка обработки сообщения от {user_id}: {e}")
            await message.answer("❌ Произошла ошибка при обработке вашего сообщения. Попробуйте еще раз.")

    async def transcribe_voice_file(self, file_path: str) -> Optional[str]:
        """Транскрибирует голосовой файл через OpenAI Whisper API."""
        api_key = os.getenv("OPENAI_API_KEY")
        if not api_key:
            logger.error("OPENAI_API_KEY не задан — транскрипция недоступна")
            return None
        url = "https://api.openai.com/v1/audio/transcriptions"
        form = aiohttp.FormData()
        form.add_field("model", "whisper-1")
        # Telegram voice обычно в ogg/opus — Whisper поддерживает
        form.add_field(
            name="file",
            value=open(file_path, "rb"),
            filename=os.path.basename(file_path),
            content_type="audio/ogg",
        )
        headers = {
            "Authorization": f"Bearer {api_key}",
        }
        timeout = aiohttp.ClientTimeout(total=90)
        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(url, data=form, headers=headers) as resp:
                if resp.status != 200:
                    txt = await resp.text()
                    logger.error(f"OpenAI Whisper error {resp.status}: {txt}")
                    return None
                data = await resp.json()
                # Ожидаем поле 'text'
                return data.get("text") if isinstance(data, dict) else None

    def sanitize_plain_text_for_telegram(self, text: str) -> str:
        """Убирает markdown-артефакты для корректного plain-text отображения в Telegram.
        Не использует внешние зависимости и не ломает обычный текст.
        """
        if not text:
            return text

        # Если пришла JSON-строка в кавычках — снимем внешние кавычки
        if len(text) >= 2 and ((text[0] == '"' and text[-1] == '"') or (text[0] == "'" and text[-1] == "'")):
            text = text[1:-1]

        # Нормализуем переводы строк и экранированные последовательности
        # NBSP → обычный пробел
        text = text.replace("\u00a0", " ")
        # 1) Уберём двойные пробелы-переносы для реальных переносов «␠␠\n»
        text = text.replace("  \n", "\n").replace("\r\n", "\n")
        # 2) И для экранированных переносов «␠␠\\n»
        text = text.replace("  \\n", "\n").replace("  \\r\\n", "\n")
        # 3) Преобразуем литералы "\\r\\n" и "\\n" в реальные переводы строк
        text = text.replace("\\r\\n", "\n").replace("\\n", "\n")
        # 4) Простейшее распознавание экранированных кавычек
        text = text.replace('\\"', '"').replace("\\'", "'")

        cleaned_lines = []
        code_block = False
        for line in text.split("\n"):
            stripped = line.strip()

            # Пропускаем горизонтальные линии и маркеры код-блоков
            if stripped in ("---", "***", "___"):
                continue
            if stripped.startswith("```"):
                code_block = not code_block
                # Не добавляем сами маркеры
                continue
            if code_block:
                # В код-блоках оставляем как есть
                # Убираем пробелы в конце строк (устраняем «  » перед переводом)
                cleaned_lines.append(line.rstrip())
                continue

            # Убираем цитирование >
            if stripped.startswith("> "):
                line = line.replace("> ", "", 1)

            # Преобразуем заголовки '#', '##', '###' в обычную строку
            if stripped.startswith("#"):
                line = stripped.lstrip("# ")

            cleaned_lines.append(line)

        text = "\n".join(cleaned_lines)

        # Убираем простые маркеры форматирования без regex
        # Сначала двойные, потом одиночные, чтобы не ломать последовательности
        for token in ("**", "__", "``"):
            text = text.replace(token, "")
        for token in ("*", "_", "`"):
            text = text.replace(token, "")

        # Сжимаем избыточные пустые строки
        while "\n\n\n" in text:
            text = text.replace("\n\n\n", "\n\n")

        return text.strip()
    
    async def send_long_message(self, chat_id: int, text: str, max_length: int = 4000, parse_mode: Optional[str] = None):
        """Отправляет длинное сообщение, разбивая на части если необходимо"""
        if len(text) <= max_length:
            await self.bot.send_message(chat_id, text, parse_mode=parse_mode)
        else:
            # Разбиваем на части
            parts = []
            current_part = ""
            
            for line in text.split('\n'):
                if len(current_part) + len(line) + 1 <= max_length:
                    current_part += line + '\n'
                else:
                    if current_part:
                        parts.append(current_part.strip())
                    current_part = line + '\n'
            
            if current_part:
                parts.append(current_part.strip())
            
            # Отправляем части
            for i, part in enumerate(parts):
                if i == 0:
                    await self.bot.send_message(chat_id, part, parse_mode=parse_mode)
                else:
                    await self.bot.send_message(chat_id, f"📄 Продолжение...\n\n{part}", parse_mode=parse_mode)
                
                # Небольшая задержка между сообщениями
                await asyncio.sleep(0.5)
    
    async def download_voice_file(self, file_id: str) -> Optional[str]:
        """Скачивает голосовой файл от Telegram"""
        try:
            file_info = await self.bot.get_file(file_id)
            temp_filename = f"temp_voice_{file_id}.ogg"
            await self.bot.download_file(file_info.file_path, temp_filename)
            return temp_filename
        except Exception as e:
            logger.error(f"❌ Ошибка скачивания голосового файла: {e}")
            return None
    
    def get_welcome_message(self, user_name: str) -> str:
        """Создает приветственное сообщение"""
        return f"""
<b>🏭 Добро пожаловать в Новосибирский крановый завод Авангард, {user_name}!</b>

Привет, <b>{user_name}</b>! 👋

Я ваш персональный консультант по продукции и услугам завода Авангард.

<b>Что я умею:</b>
• 🏢 Информация о компании из базы знаний
• 🔍 Поиск информации о товарах и услугах
• 🌐 Актуальная информация из интернета
• 📄 Работать с документами и прайс-листами
• 📧 Отправлять email при необходимости
• 🎤 Понимать голосовые сообщения

<b>Просто напишите ваш вопрос или отправьте голосовое сообщение!</b>

Для получения справки используйте /help
        """
    

    
    def get_app(self):
        """Создает aiohttp приложение для webhook"""
        app = web.Application()
        
        # Настройка webhook handler
        webhook_requests_handler = SimpleRequestHandler(
            dispatcher=self.dp,
            bot=self.bot,
        )
        webhook_requests_handler.register(app, path="/telegram/webhook")
        
        # Health check endpoint
        async def health_check(request):
            return web.json_response({
                "status": "Avangard Telegram Bot is running",
                "agent": "avangard_consultant",
                "version": "2.0"
            })
        
        app.router.add_get("/", health_check)
        app.router.add_get("/health", health_check)
        
        setup_application(app, self.dp, bot=self.bot)
        return app
    
    async def start_webhook(self, webhook_url: str):
        """Устанавливает webhook"""
        await self.bot.set_webhook(
            url=webhook_url,
            drop_pending_updates=True
        )
        logger.info(f"✅ Webhook установлен: {webhook_url}")
    
    async def start_polling(self):
        """Запускает бота в режиме polling"""
        await self.bot.delete_webhook(drop_pending_updates=True)
        logger.info("🚀 Avangard Telegram Bot запущен в режиме polling")
        await self.dp.start_polling(self.bot)
    
    async def serve_webhook_async(self, port: int = 8080, webhook_url: str = None):
        """Запускает бота в режиме webhook (асинхронно)"""
        if webhook_url:
            await self.start_webhook(webhook_url)
        
        app = self.get_app()
        
        logger.info(f"🤖 Avangard Telegram Bot v2.0 запущен на порту {port}")
        logger.info(f"📡 Webhook URL: http://localhost:{port}/telegram/webhook")
        logger.info(f"🔍 Health check: http://localhost:{port}/health")
        
        runner = web.AppRunner(app)
        await runner.setup()
        
        site = web.TCPSite(runner, "0.0.0.0", port)
        await site.start()
        
        # Держим сервер запущенным
        try:
            await asyncio.Future()  # run forever
        except KeyboardInterrupt:
            logger.info("🛑 Остановка Avangard Telegram Bot...")
        finally:
            await runner.cleanup()


async def main():
    """Главная функция запуска бота"""
    # Получаем токен из переменных окружения
    telegram_token = os.getenv("TELEGRAM_TOKEN")
    if not telegram_token:
        logger.error("❌ TELEGRAM_TOKEN не найден в переменных окружения!")
        return
    
    # Создаем бота
    bot = AvangardTelegramBot(token=telegram_token)
    
    # Выбираем режим запуска
    webhook_url = os.getenv("TELEGRAM_WEBHOOK_URL")
    port = int(os.getenv("TELEGRAM_PORT", "8080"))
    
    if webhook_url:
        # Режим webhook
        logger.info(f"🚀 Запуск в режиме webhook: {webhook_url}")
        await bot.serve_webhook_async(port=port, webhook_url=webhook_url)
    else:
        # Режим polling
        logger.info("🚀 Запуск в режиме polling")
        await bot.start_polling()


if __name__ == "__main__":
    # Запускаем бота
    asyncio.run(main())
