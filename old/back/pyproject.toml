[project]
name = "agent-api"
version = "0.1.0"
requires-python = ">=3.11"
readme = "README.md"
authors = [{ name = "Agno", email = "<EMAIL>" }]

dependencies = [
  "agno==1.4.6",
  "aiofiles",                 # ← Для асинхронной работы с файлами
  "duckduckgo-search",
  "exa-py",                    # для ExaTools
  "fastapi[standard]",
  "google-api-python-client",  # для Google Sheets
  "google-auth-httplib2",      # для Google Sheets auth
  "google-auth-oauthlib",      # для Google Sheets OAuth
  "python-docx",              # ← Добавить эту строку
  "pandas",                   # ← Для CSV
  "textract",                 # ← Для лучшей обработки документов
  "pypdf",                    # ← Для PDF обработки
  "openai",
  "pgvector",
  "psycopg[binary]",
  "sqlalchemy",
  "yfinance",
  "qdrant-client",            # для работы с Qdrant
  "aiogram",
]

[project.optional-dependencies]
dev = ["mypy", "ruff"]

[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[tool.setuptools.packages.find]

[tool.ruff]
line-length = 120
exclude = [".venv*"]
[tool.ruff.lint.per-file-ignores]
# Ignore `F401` (import violations) in all `__init__.py` files
"__init__.py" = ["F401", "F403"]

[tool.mypy]
check_untyped_defs = true
no_implicit_optional = true
warn_unused_configs = true
plugins = ["pydantic.mypy"]
exclude = [".venv*"]

[[tool.mypy.overrides]]
module = ["pgvector.*", "setuptools.*", "nest_asyncio.*", "agno.*"]
ignore_missing_imports = true

[tool.uv.pip]
no-annotate = true
