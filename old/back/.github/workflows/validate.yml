name: Validate

on:
  push:
    branches:
      - "main"
  pull_request:
    types:
      - opened
      - synchronize
      - reopened
    branches:
      - "main"

jobs:
  validate:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11", "3.12"]
      fail-fast: false

    steps:
      - uses: actions/checkout@v4

      - name: Install uv
        uses: astral-sh/setup-uv@v3
        with:
          enable-cache: true
          cache-dependency-glob: "requirements**.txt"

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ matrix.python-version }}

      - name: Create a virtual environment
        run: uv venv --python ${{ matrix.python-version }}

      - name: Install dependencies
        run: |
          uv pip sync requirements.txt
          uv pip install ruff mypy

      - name: Format with ruff
        run: uv run ruff format . --check

      - name: Lint with ruff
        run: uv run ruff check .

      - name: Type-check with mypy
        run: uv run mypy .