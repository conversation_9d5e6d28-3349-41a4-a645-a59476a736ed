from typing import Optional
from agno.agent import Agent, AgentKnowledge
from agno.models.openai import OpenAIChat
from agno.memory.v2.db.postgres import PostgresMemoryDb
from agno.memory.v2.memory import Memory
from agno.storage.agent.postgres import PostgresAgentStorage
from agno.embedder.openai import OpenAIEmbedder
from agno.knowledge.document import DocumentKnowledgeBase
from agno.knowledge.pdf import PDFKnowledgeBase, PDFReader  
from agno.knowledge.combined import CombinedKnowledgeBase
from agno.vectordb.pgvector import PgVector, SearchType
from agno.document import Document
from agno.document.chunking.fixed import FixedSizeChunking
from agno.tools.exa import ExaTools
from agent_tools.smtp_email_tool import smtp_email
from agent_prompts.consultant_instructions import CONSULTANT_INSTRUCTIONS
from agno.tools.openai import OpenAITools
from db.session import db_url
import textract
import os



def get_avangard_knowledge() -> CombinedKnowledgeBase:
    """Создает комбинированную базу знаний - точно по документации Agno."""
    
    # PDF база знаний
    pdf_kb = PDFKnowledgeBase(
        path="agent_docs_pdf",
        vector_db=PgVector(
            table_name="avangard_pdf_knowledge",
            db_url=db_url,
        ),
    )
    
    # Документы (CSV, DOCX, etc.) - принудительная загрузка
    import os
    from agno.document import Document
    import textract
    
    documents = []
    doc_folder = "agent_docs_other"
    if os.path.exists(doc_folder):
        for filename in os.listdir(doc_folder):
            file_path = os.path.join(doc_folder, filename)
            if os.path.isfile(file_path):
                try:
                    text_bytes = textract.process(file_path)
                    content = text_bytes.decode('utf-8')
                    doc = Document(
                        name=filename,
                        id=filename,
                        content=content,
                        meta_data={"source": file_path, "size": os.path.getsize(file_path)}
                    )
                    documents.append(doc)
                    print(f"✅ Loaded document: {filename} ({len(content)} chars)")
                except Exception as e:
                    print(f"❌ Error loading {filename}: {e}")
    
    doc_kb = DocumentKnowledgeBase(
        path="agent_docs_other",
        vector_db=PgVector(
            table_name="avangard_doc_knowledge", 
            db_url=db_url,
        ),
        documents=documents,
    )
    
    # Комбинированная база знаний - БЕЗ принудительной загрузки здесь!
    return CombinedKnowledgeBase(
        sources=[pdf_kb, doc_kb],
        vector_db=PgVector(
            table_name="avangard_combined_knowledge",
            db_url=db_url,
        ),
    )


def get_avangard_consultant(
    model_id: str = "gpt-4o-mini",
    user_id: Optional[str] = None,
    session_id: Optional[str] = None,
    debug_mode: bool = True,
):
    """Фабрика агента Avangard Consultant для FastAPI API с DocumentKnowledgeBase."""
    
    # Настройка Google Sheets credentials
    google_credentials_path = os.getenv('GOOGLE_CREDENTIALS_PATH', 'meta-geography-464110-b9-55c203771d7e.json')
    if google_credentials_path and os.path.exists(google_credentials_path):
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = google_credentials_path
        if debug_mode:
            print(f"🔑 Google Sheets credentials: {google_credentials_path}")
    else:
        if debug_mode:
            print("⚠️ Google Sheets credentials not found")
    agent = Agent(
        name="Avangard Consultant",
        agent_id="avangard_consultant",
        user_id=user_id,
        session_id=session_id,
        model=OpenAIChat(id=model_id, max_tokens=4000, temperature=0.5),
        # База знаний из локальных документов
        knowledge=get_avangard_knowledge(),
        search_knowledge=True,
        enable_agentic_knowledge_filters=True,  # Автоматические фильтры поиска
        tools=[
            ExaTools(),  # интернет-поиск
            smtp_email,  # отправка email
            OpenAITools(transcription_model="whisper-1"),  # транскрипция
        ],
        show_tool_calls=True,
        add_datetime_to_instructions=True,
        timezone_identifier="Asia/Novosibirsk",
        instructions=CONSULTANT_INSTRUCTIONS.split('\n\n') if isinstance(CONSULTANT_INSTRUCTIONS, str) else CONSULTANT_INSTRUCTIONS,
        # Хранилище и память в Postgres
        storage=PostgresAgentStorage(table_name="avangard_consultant_sessions", db_url=db_url),
        memory=Memory(
            model=OpenAIChat(id=model_id),
            db=PostgresMemoryDb(table_name="avangard_memories", db_url=db_url),
            delete_memories=True,
            clear_memories=True,
        ),
        enable_agentic_memory=True,
        add_history_to_messages=True,
        num_history_runs=3,
        read_chat_history=True,
        markdown=False,
        debug_mode=debug_mode,
    )
    
    # Автоматическая загрузка базы знаний при инициализации с отладкой
    try:
        if debug_mode:
            print(f"🔄 Loading combined knowledge base...")
            print(f"📁 PDF files: agent_docs_pdf")
            print(f"📁 Other docs: agent_docs_other")
            
            # Проверим файлы в папках
            for folder in ["agent_docs_pdf", "agent_docs_other"]:
                if os.path.exists(folder):
                    files = os.listdir(folder)
                    print(f"📂 Files in {folder}: {files}")
                    for file in files:
                        file_path = os.path.join(folder, file)
                        size = os.path.getsize(file_path)
                        print(f"  - {file} ({size} bytes)")
                else:
                    print(f"❌ Directory '{folder}' not found!")
        
        # Загружаем базу знаний только если она еще не загружена
        # Проверяем есть ли уже данные в векторной базе
        if hasattr(agent.knowledge, 'vector_db'):
            try:
                # Пытаемся получить количество документов
                existing_docs = agent.knowledge.vector_db.read()
                if existing_docs and len(existing_docs) > 0:
                    if debug_mode:
                        print(f"✅ Knowledge base already loaded with {len(existing_docs)} documents")
                else:
                    agent.knowledge.load(recreate=True)  # Не пересоздаем, только загружаем если нужно
            except:
                # Если ошибка при чтении, загружаем заново
                agent.knowledge.load(recreate=True)
        else:
            agent.knowledge.load(recreate=True)
        
        if debug_mode:
            # CombinedKnowledgeBase имеет другую структуру
            if hasattr(agent.knowledge, 'sources'):
                print(f"✅ Combined knowledge base loaded with {len(agent.knowledge.sources)} sources")
                for i, source in enumerate(agent.knowledge.sources):
                    source_name = source.__class__.__name__
                    print(f"  📋 {source_name}: Ready for search")
                    
                    # Показать информацию о таблице
                    if hasattr(source, 'vector_db') and hasattr(source.vector_db, 'table_name'):
                        table_name = source.vector_db.table_name
                        print(f"       Table: {table_name}")
                
                print(f"📊 Knowledge base ready for search across all sources")
            else:
                print("⚠️ No sources found in combined knowledge base!")
                
    except Exception as e:
        if debug_mode:
            print(f"❌ Error loading knowledge base: {e}")
            import traceback
            traceback.print_exc()
    
    return agent

