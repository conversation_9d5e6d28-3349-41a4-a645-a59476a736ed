#!/usr/bin/env python3
"""
Скачивание документов из Google Drive в папки для базы знаний агента Avangard.
"""

import os
import io
from pathlib import Path
from googleapiclient.discovery import build
from google.oauth2 import service_account
from googleapiclient.http import MediaIoBaseDownload
from dotenv import load_dotenv

# Загружаем переменные окружения
load_dotenv()

class GoogleDocsDownloader:
    def __init__(self):
        self.service_account_file = os.getenv('GOOGLE_SERVICE_ACCOUNT_FILE')
        self.folder_id = os.getenv('GOOGLE_DRIVE_FOLDER_ID')
        
        if not self.service_account_file or not self.folder_id:
            raise ValueError("Не найдены GOOGLE_SERVICE_ACCOUNT_FILE или GOOGLE_DRIVE_FOLDER_ID в .env")
        
        # Настройка аутентификации
        credentials = service_account.Credentials.from_service_account_file(
            self.service_account_file,
            scopes=['https://www.googleapis.com/auth/drive.readonly']
        )
        
        self.service = build('drive', 'v3', credentials=credentials)
        
        # Создаем папки для базы знаний
        self.pdf_folder = Path("agent_docs_pdf")
        self.other_folder = Path("agent_docs_other")
        
        self.pdf_folder.mkdir(exist_ok=True)
        self.other_folder.mkdir(exist_ok=True)
        
        print(f"📁 PDF файлы будут сохранены в: {self.pdf_folder}")
        print(f"📁 Другие файлы будут сохранены в: {self.other_folder}")

    def get_files_from_folder(self):
        """Получает список всех файлов из папки Google Drive."""
        print(f"🔍 Получаем список файлов из папки: {self.folder_id}")
        
        try:
            # Получаем все файлы из папки
            results = self.service.files().list(
                q=f"'{self.folder_id}' in parents and trashed=false",
                fields="files(id, name, mimeType, size)"
            ).execute()
            
            files = results.get('files', [])
            print(f"📊 Найдено файлов: {len(files)}")
            
            return files
            
        except Exception as e:
            print(f"❌ Ошибка при получении списка файлов: {e}")
            return []

    def download_file(self, file_id, file_name, mime_type):
        """Скачивает файл из Google Drive."""
        try:
            # Определяем формат для скачивания
            if mime_type == 'application/vnd.google-apps.document':
                # Google Docs -> DOCX
                request = self.service.files().export_media(fileId=file_id, mimeType='application/vnd.openxmlformats-officedocument.wordprocessingml.document')
                file_name = f"{file_name}.docx"
            elif mime_type == 'application/vnd.google-apps.spreadsheet':
                # Google Sheets -> CSV
                request = self.service.files().export_media(fileId=file_id, mimeType='text/csv')
                file_name = f"{file_name}.csv"
            elif mime_type == 'application/vnd.google-apps.presentation':
                # Google Slides -> PDF
                request = self.service.files().export_media(fileId=file_id, mimeType='application/pdf')
                file_name = f"{file_name}.pdf"
            else:
                # Обычные файлы
                request = self.service.files().get_media(fileId=file_id)
            
            # Скачиваем файл
            file_io = io.BytesIO()
            downloader = MediaIoBaseDownload(file_io, request)
            
            done = False
            while done is False:
                status, done = downloader.next_chunk()
            
            return file_io.getvalue(), file_name
            
        except Exception as e:
            print(f"❌ Ошибка при скачивании файла {file_name}: {e}")
            return None, None

    def save_file(self, content, file_name):
        """Сохраняет файл в соответствующую папку."""
        if not content:
            return False
        
        # Определяем папку по расширению
        file_extension = Path(file_name).suffix.lower()
        
        if file_extension == '.pdf':
            target_folder = self.pdf_folder
            file_type = "PDF"
        else:
            target_folder = self.other_folder
            file_type = "OTHER"
        
        # Сохраняем файл
        file_path = target_folder / file_name
        
        try:
            with open(file_path, 'wb') as f:
                f.write(content)
            
            file_size = len(content)
            print(f"✅ {file_type}: {file_name} ({file_size} bytes) → {file_path}")
            return True
            
        except Exception as e:
            print(f"❌ Ошибка при сохранении {file_name}: {e}")
            return False

    def download_all_files(self):
        """Скачивает все файлы из папки Google Drive."""
        print("🚀 Начинаем скачивание файлов из Google Drive...")
        
        files = self.get_files_from_folder()
        
        if not files:
            print("⚠️ Файлы не найдены!")
            return
        
        downloaded_count = 0
        pdf_count = 0
        other_count = 0
        
        for file_info in files:
            file_id = file_info['id']
            file_name = file_info['name']
            mime_type = file_info['mimeType']
            file_size = file_info.get('size', 'unknown')
            
            print(f"\n🔄 Обрабатываем: {file_name}")
            print(f"   Тип: {mime_type}")
            print(f"   Размер: {file_size} bytes")
            
            # Скачиваем файл
            content, final_name = self.download_file(file_id, file_name, mime_type)
            
            if content and final_name:
                # Сохраняем файл
                if self.save_file(content, final_name):
                    downloaded_count += 1
                    
                    # Считаем по типам
                    if Path(final_name).suffix.lower() == '.pdf':
                        pdf_count += 1
                    else:
                        other_count += 1
        
        print(f"\n🎉 Скачивание завершено!")
        print(f"📊 Всего скачано: {downloaded_count} файлов")
        print(f"📄 PDF файлов: {pdf_count} → {self.pdf_folder}")
        print(f"📋 Других файлов: {other_count} → {self.other_folder}")

def main():
    """Основная функция."""
    print("=" * 60)
    print("🔽 Google Drive Downloader для агента Avangard")
    print("=" * 60)
    
    try:
        downloader = GoogleDocsDownloader()
        downloader.download_all_files()
        
    except Exception as e:
        print(f"❌ Критическая ошибка: {e}")
        return 1
    
    print("\n✅ Готово! Файлы скачаны в папки для базы знаний.")
    print("Теперь можно перезапустить агента для обновления базы знаний.")
    
    return 0

if __name__ == "__main__":
    exit(main())