#!/usr/bin/env python3
"""
ОПТИМИЗИРОВАННЫЙ агент с Google Sheets MCP сервером
"""

import asyncio
import os
from dotenv import load_dotenv
from agno.agent import Agent
from agno.models.openai import OpenAIChat
from agno.tools.mcp import MCPTools
from agno.storage.sqlite import SqliteStorage

async def run_google_sheets_agent():
    """ОПТИМИЗИРОВАННЫЙ агент для работы с Google Sheets"""
    
    # Загружаем .env
    load_dotenv()
    
    # Устанавливаем переменные окружения
    os.environ["GOOGLE_CREDENTIALS_PATH"] = "/home/<USER>/My/sibavangard4/meta-geography-464110-b9-55c203771d7e.json"
    os.environ["GOOGLE_SPREADSHEET_ID"] = "1Mqak3XJtkwC-UJ1ZGihGNm28trUwpVQfX3EKpY9iMHo"
    
    # Создаем MCP tools с контекстным менеджером
    async with MCPTools(
        command="python3 mcp_server.py",
        timeout_seconds=30  # Уменьшили с 60 до 30
    ) as mcp_tools:
        
        # ОПТИМИЗИРОВАННЫЙ агент
        agent = Agent(
            model=OpenAIChat(
                id="gpt-4o-mini",
                temperature=0.1,      # Меньше креативности = быстрее
                max_tokens=800,       # Ограничиваем длину ответов
                timeout=15            # Таймаут для модели
            ),
            tools=[mcp_tools],
            instructions="""
            Ты помощник для Google Sheets. ЗАПОМНИ ПРОСТЫЕ ПРАВИЛА:
            
            🔍 ПОИСК ТОВАРА:
            search_product("название товара") - найти товар
            
            📊 УЗНАТЬ СТРУКТУРУ:
            get_all_sheets_structure() - узнать какие колонки в каких листах
            
              ИЗМЕНИТЬ ДАННЫЕ:
              write_sheet(spreadsheet_id, "ЛистНазвание!КолонкаСтрока", [["новое значение"]])

              ИЗМЕНИТЬ КОЛИЧЕСТВО:
              update_product_quantity(spreadsheet_id, "лист", строка, "колонка", "новое_количество")
            
            💡 ЛОГИКА:
            1. Если ищут товар → search_product
            2. Если нужно изменить что-то → get_all_sheets_structure (узнать колонки)
            3. Потом write_sheet с правильными координатами
            
            🎯 КОГДА ГОВОРЯТ "этот товар", "первая позиция" - используй данные из предыдущего поиска!
            НЕ ищи заново!
            
            Spreadsheet ID всегда: 1Mqak3XJtkwC-UJ1ZGihGNm28trUwpVQfX3EKpY9iMHo
            """,
            # ОПТИМИЗИРОВАННАЯ память
            storage=SqliteStorage(
                table_name="google_sheets_sessions", 
                db_file="tmp/agent_memory.db"
            ),
            add_history_to_messages=True,
            num_history_runs=3,        # Уменьшили с 5 до 1
            show_tool_calls=True,
            markdown=False,            # Отключили markdown
            debug_mode=True,          # Отключили debug
            stream_intermediate_steps=False  # Отключили промежуточные шаги
        )
        
        # Интерактивный режим
        print("🚀 ОПТИМИЗИРОВАННЫЙ Google Sheets агент запущен!")
        print("⚡ Быстрые ответы, минимум токенов")
        print("💡 Введите 'exit' для выхода\n")
        
        while True:
            try:
                user_input = input("Вы: ").strip()
                
                if user_input.lower() in ['exit', 'quit', 'выход']:
                    break
                
                if user_input:
                    # Оптимизированный вызов без stream для скорости
                    await agent.aprint_response(user_input, stream=False)
                    
            except KeyboardInterrupt:
                break
        
        print("\n👋 До свидания!")

if __name__ == "__main__":
    # Создаем папку tmp если её нет
    os.makedirs("tmp", exist_ok=True)
    asyncio.run(run_google_sheets_agent())
