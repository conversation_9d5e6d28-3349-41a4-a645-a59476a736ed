'use client'
import { Button } from '@/components/ui/button'
import Icon from '@/components/ui/icon'

interface AttachedFile {
  file: File
  id: string
}

interface AttachedFilesProps {
  files: AttachedFile[]
  onRemoveFile: (id: string) => void
  onClearAll: () => void
}

const AttachedFiles = ({ files, onRemoveFile, onClearAll }: AttachedFilesProps) => {
  if (files.length === 0) return null

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) return 'image'
    if (file.type.startsWith('video/')) return 'video'
    if (file.type.startsWith('audio/')) return 'volume2'
    if (file.type === 'application/pdf') return 'file-text'
    if (file.type.includes('document')) return 'file-text'
    if (file.type === 'text/csv') return 'table'
    if (file.type === 'application/json') return 'braces'
    return 'file'
  }

  return (
    <div className="mb-2 rounded-lg border border-accent bg-primaryAccent p-2">
      <div className="mb-2 flex items-center justify-between">
        <span className="text-xs font-medium text-muted-foreground">
          {files.length} FILE{files.length > 1 ? 'S' : ''} ATTACHED
        </span>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          onClick={onClearAll}
          className="h-6 px-2 text-xs text-muted-foreground hover:text-primary"
        >
          ✕ CLEAR
        </Button>
      </div>
      <div className="space-y-1">
        {files.map((attachedFile) => (
          <div
            key={attachedFile.id}
            className="flex items-center justify-between rounded bg-background p-2"
          >
            <div className="flex items-center space-x-2 flex-1 min-w-0">
              <Icon type={getFileIcon(attachedFile.file)} className="h-4 w-4 text-muted-foreground flex-shrink-0" />
              <div className="min-w-0 flex-1">
                <div className="truncate text-sm font-medium text-primary">
                  {attachedFile.file.name}
                </div>
                <div className="text-xs text-muted-foreground">
                  {formatFileSize(attachedFile.file.size)}
                </div>
              </div>
            </div>
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => onRemoveFile(attachedFile.id)}
              className="h-6 w-6 p-0 text-muted-foreground hover:text-primary flex-shrink-0"
            >
              ✕
            </Button>
          </div>
        ))}
      </div>
    </div>
  )
}

export default AttachedFiles