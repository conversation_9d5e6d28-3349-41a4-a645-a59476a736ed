'use client'
import { useState } from 'react'
import { toast } from 'sonner'
import { TextArea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { usePlaygroundStore } from '@/store'
import useAIChatStreamHandler from '@/hooks/useAIStreamHandler'
import { useQueryState } from 'nuqs'
import Icon from '@/components/ui/icon'
import FileUpload from './FileUpload'
import AttachedFiles from './AttachedFiles'

interface AttachedFile {
  file: File
  id: string
}

const ChatInput = () => {
  const { chatInputRef } = usePlaygroundStore()

  const { handleStreamResponse } = useAIChatStreamHandler()
  const [selectedAgent] = useQueryState('agent')
  const [inputMessage, setInputMessage] = useState('')
  const [attachedFiles, setAttachedFiles] = useState<AttachedFile[]>([])
  const isStreaming = usePlaygroundStore((state) => state.isStreaming)

  const handleFilesSelected = (files: File[]) => {
    const newAttachedFiles = files.map(file => ({
      file,
      id: `${file.name}-${Date.now()}-${Math.random()}`
    }))
    setAttachedFiles(prev => [...prev, ...newAttachedFiles])
  }

  const handleRemoveFile = (id: string) => {
    setAttachedFiles(prev => prev.filter(f => f.id !== id))
  }

  const handleClearAllFiles = () => {
    setAttachedFiles([])
  }

  const handleSubmit = async () => {
    if (!inputMessage.trim() && attachedFiles.length === 0) return

    const currentMessage = inputMessage
    const currentFiles = attachedFiles
    
    setInputMessage('')
    setAttachedFiles([])

    try {
      // Создаем FormData для отправки файлов
      const formData = new FormData()
      formData.append('message', currentMessage)
      
      // Добавляем файлы в FormData
      currentFiles.forEach((attachedFile, index) => {
        formData.append(`files`, attachedFile.file)
      })

      await handleStreamResponse(formData)
    } catch (error) {
      toast.error(
        `Error in handleSubmit: ${
          error instanceof Error ? error.message : String(error)
        }`
      )
    }
  }

  return (
    <div className="relative mx-auto mb-1 w-full max-w-2xl font-geist">
      <AttachedFiles
        files={attachedFiles}
        onRemoveFile={handleRemoveFile}
        onClearAll={handleClearAllFiles}
      />
      <div className="flex items-end justify-center gap-x-2">
        <div className="flex flex-col flex-1">
          <div className="flex items-end gap-x-2">
            {/* <FileUpload
              onFilesSelected={handleFilesSelected}
              disabled={!selectedAgent || isStreaming}
            /> */}
            <TextArea
              placeholder={'Задайте вопрос'}
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              onKeyDown={(e) => {
                if (
                  e.key === 'Enter' &&
                  !e.nativeEvent.isComposing &&
                  !e.shiftKey &&
                  !isStreaming
                ) {
                  e.preventDefault()
                  handleSubmit()
                }
              }}
              className="w-full border border-accent bg-primaryAccent px-4 text-sm text-primary focus:border-accent"
              disabled={!selectedAgent}
              ref={chatInputRef}
            />
            <Button
              onClick={handleSubmit}
              disabled={!selectedAgent || (!inputMessage.trim() && attachedFiles.length === 0) || isStreaming}
              size="icon"
              className="rounded-xl bg-primary p-5 text-primaryAccent"
            >
              <Icon type="send" color="primaryAccent" />
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default ChatInput
