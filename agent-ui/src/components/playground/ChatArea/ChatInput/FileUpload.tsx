'use client'
import { useState, useRef } from 'react'
import { Button } from '@/components/ui/button'
import Icon from '@/components/ui/icon'
import { toast } from 'sonner'

interface FileUploadProps {
  onFilesSelected: (files: File[]) => void
  disabled?: boolean
}

const FileUpload = ({ onFilesSelected, disabled }: FileUploadProps) => {
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [isDragOver, setIsDragOver] = useState(false)

  const handleFileSelect = (files: FileList | null) => {
    if (!files) return

    const fileArray = Array.from(files)
    const validFiles = fileArray.filter(file => {
      // Поддерживаемые типы файлов
      const supportedTypes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/csv',
        'application/json',
        'text/plain',
        'text/markdown',
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/webp',
        'audio/mpeg',
        'audio/wav',
        'audio/ogg',
        'video/mp4',
        'video/webm',
        'video/quicktime'
      ]
      
      if (!supportedTypes.includes(file.type)) {
        toast.error(`Неподдерживаемый тип файла: ${file.name}`)
        return false
      }

      // Ограничение размера файла (50MB)
      const maxSize = 50 * 1024 * 1024
      if (file.size > maxSize) {
        toast.error(`Файл слишком большой: ${file.name} (максимум 50MB)`)
        return false
      }

      return true
    })

    if (validFiles.length > 0) {
      onFilesSelected(validFiles)
    }
  }

  const handleButtonClick = () => {
    fileInputRef.current?.click()
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(true)
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
  }

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    setIsDragOver(false)
    handleFileSelect(e.dataTransfer.files)
  }

  return (
    <div
      className={`relative ${isDragOver ? 'opacity-50' : ''}`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept=".pdf,.docx,.csv,.json,.txt,.md,.jpg,.jpeg,.png,.gif,.webp,.mp3,.wav,.ogg,.mp4,.webm,.mov"
        onChange={(e) => handleFileSelect(e.target.files)}
        className="hidden"
      />
      <Button
        type="button"
        variant="ghost"
        size="icon"
        onClick={handleButtonClick}
        disabled={disabled}
        className="h-8 w-8 text-muted-foreground hover:text-primary"
        title="Прикрепить файл"
      >
        <Icon type="paperclip" />
      </Button>
    </div>
  )
}

export default FileUpload