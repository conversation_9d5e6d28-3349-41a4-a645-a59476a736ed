import { NextResponse } from 'next/server'
import { spawn } from 'child_process'

export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

// Absolute paths
const REBUILD_SCRIPT = '/home/<USER>/My/agent-api/agent-api/rebuild_knowledge.py'
const VENV_DIR = '/home/<USER>/My/agent-api/agent-api/.venv'
const VENV_PYTHON = `${VENV_DIR}/bin/python`

export async function POST() {
  return new Promise<NextResponse>((resolve) => {
    try {
      // Prefer virtualenv python if present
      const pythonBin = VENV_PYTHON
      const env = {
        ...process.env,
        VIRTUAL_ENV: VENV_DIR,
        PATH: `${VENV_DIR}/bin:${process.env.PATH || ''}`,
      }
      const child = spawn(pythonBin, [REBUILD_SCRIPT], {
        cwd: '/home/<USER>/My/agent-api/agent-api',
        env,
      })

      let output = ''

      child.stdout.on('data', (data) => {
        output += data.toString()
      })

      child.stderr.on('data', (data) => {
        output += data.toString()
      })

      child.on('close', (code) => {
        if (code === 0) {
          resolve(NextResponse.json({ success: true, log: output }))
        } else {
          resolve(NextResponse.json({ success: false, code, log: output }, { status: 500 }))
        }
      })

      child.on('error', (err) => {
        resolve(NextResponse.json({ success: false, error: String(err) }, { status: 500 }))
      })
    } catch (e: any) {
      resolve(NextResponse.json({ success: false, error: e?.message || 'Unknown error' }, { status: 500 }))
    }
  })
}
