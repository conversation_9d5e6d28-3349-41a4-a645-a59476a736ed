import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import { spawn, exec } from 'child_process'
import path from 'path'
import { promisify } from 'util'

const execAsync = promisify(exec)
// Paths from agent-ui to backend (agent-api)
const PROJECT_DIR = path.join(process.cwd(), '..', 'agent-api')
const INSTRUCTIONS_DIR = path.join(PROJECT_DIR, 'agent_prompts')
const VENV_DIR = path.join(PROJECT_DIR, '.venv')
const VENV_PYTHON = path.join(VENV_DIR, 'bin', 'python')

// Функция для остановки процессов на портах
async function killProcessOnPort(port: number, serviceName: string): Promise<boolean> {
    try {
        console.log(`🔍 Проверяем порт ${port} (${serviceName})...`)
        
        // Найти процессы на порту
        let pids: string[] = []
        try {
            const { stdout } = await execAsync(`lsof -ti:${port}`)
            pids = stdout.trim().split('\n').filter(pid => pid)
        } catch (e) {
            // Если lsof вернул ненулевой код (нет процессов или нет lsof) — считаем порт свободным
            console.log(`✅ Порт ${port} свободен (lsof недоступен/процессы не найдены)`)
            return true
        }
        
        if (pids.length === 0) {
            console.log(`✅ Порт ${port} свободен`)
            return true
        }
        
        console.log(`⚠️ Найдены процессы на порту ${port}: ${pids.join(', ')}`)
        
        // Мягкое завершение
        for (const pid of pids) {
            try {
                await execAsync(`kill ${pid}`)
                console.log(`🔫 Отправлен SIGTERM процессу ${pid}`)
            } catch (error) {
                console.log(`⚠️ Процесс ${pid} уже завершен`)
            }
        }
        
        // Ждем 3 секунды
        await new Promise(resolve => setTimeout(resolve, 3000))
        
        // Проверяем, остались ли процессы
        try {
            const { stdout: remainingStdout } = await execAsync(`lsof -ti:${port}`)
            const remainingPids = remainingStdout.trim().split('\n').filter(pid => pid)
            
            if (remainingPids.length > 0) {
                console.log(`💥 Принудительно завершаем оставшиеся процессы: ${remainingPids.join(', ')}`)
                for (const pid of remainingPids) {
                    try {
                        await execAsync(`kill -9 ${pid}`)
                    } catch (error) {
                        console.log(`⚠️ Не удалось убить процесс ${pid}`)
                    }
                }
            }
        } catch (error) {
            // Если lsof не находит процессы, это хорошо
            console.log(`✅ Порт ${port} освобожден`)
        }
        
        return true
    } catch (error) {
        console.error(`❌ Ошибка при остановке процессов на порту ${port}:`, error)
        return false
    }
}

// Функция для запуска сервиса с задержкой
async function startService(command: string, args: string[], cwd: string, serviceName: string, delay: number = 0): Promise<void> {
    return new Promise((resolve, reject) => {
        if (delay > 0) {
            console.log(`⏳ Ждем ${delay}ms перед запуском ${serviceName}...`)
            setTimeout(() => {
                startServiceInternal()
            }, delay)
        } else {
            startServiceInternal()
        }
        
        function startServiceInternal() {
            console.log(`🚀 Запускаем ${serviceName}...`)
            console.log(`📍 Команда: ${command} ${args.join(' ')}`)
            console.log(`📂 Директория: ${cwd}`)
            
            const child = spawn(command, args, {
                cwd,
                detached: true,
                stdio: 'ignore',
                env: { ...process.env }
            })
            
            child.unref() // Позволяем родительскому процессу завершиться
            
            child.on('spawn', () => {
                console.log(`✅ ${serviceName} запущен с PID: ${child.pid}`)
                resolve()
            })
            
            child.on('error', (error) => {
                console.error(`❌ Ошибка запуска ${serviceName}:`, error)
                reject(error)
            })
        }
    })
}

// GET - получить инструкции агента
export async function GET(request: NextRequest) {
    try {
        const { searchParams } = new URL(request.url)
        const agent = searchParams.get('agent')

        if (!agent) {
            return NextResponse.json({ error: 'Agent parameter required' }, { status: 400 })
        }

        let filePath: string
        let variableName: string

        if (agent === 'consultant') {
            filePath = path.join(INSTRUCTIONS_DIR, 'consultant_instructions.py')
            variableName = 'CONSULTANT_INSTRUCTIONS'
        } else if (agent === 'mcp') {
            filePath = path.join(INSTRUCTIONS_DIR, 'mcp_instructions.py')
            variableName = 'MCP_INSTRUCTIONS'
        } else if (agent === 'warehouse') {
            filePath = path.join(INSTRUCTIONS_DIR, 'warehouse_instructions.py')
            variableName = 'WAREHOUSE_INSTRUCTIONS'
        } else {
            return NextResponse.json({ error: 'Invalid agent' }, { status: 400 })
        }

        const content = await fs.readFile(filePath, 'utf-8')

        // Ищем triple quotes строку
        const tripleQuotePattern = new RegExp(`${variableName}\\s*=\\s*"""([\\s\\S]*?)"""`)
        const match = content.match(tripleQuotePattern)

        if (match) {
            // Если найдена triple quotes строка
            const instructions = match[1].replace(/\\"""/g, '"""')
            return NextResponse.json({
                agent,
                instructions: instructions
            })
        }

        // Fallback: пытаемся парсить как массив (для старых файлов)
        const arrayPattern = new RegExp(`${variableName}\\s*=\\s*\\[`)
        const arrayMatch = content.search(arrayPattern)

        if (arrayMatch === -1) {
            return NextResponse.json({ error: 'Instructions variable not found' }, { status: 404 })
        }

        // Находим начало массива
        const arrayStart = content.indexOf('[', arrayMatch)
        if (arrayStart === -1) {
            return NextResponse.json({ error: 'Array start not found' }, { status: 404 })
        }

        // Находим конец массива (последняя закрывающая скобка)
        let bracketCount = 0
        let arrayEnd = -1

        for (let i = arrayStart; i < content.length; i++) {
            if (content[i] === '[') bracketCount++
            if (content[i] === ']') {
                bracketCount--
                if (bracketCount === 0) {
                    arrayEnd = i
                    break
                }
            }
        }

        if (arrayEnd === -1) {
            return NextResponse.json({ error: 'Array end not found' }, { status: 404 })
        }

        // Извлекаем содержимое массива
        const arrayContent = content.substring(arrayStart + 1, arrayEnd)

        // Парсим строки более аккуратно
        const instructions = []
        let currentString = ''
        let inString = false
        let escapeNext = false

        for (let i = 0; i < arrayContent.length; i++) {
            const char = arrayContent[i]

            if (escapeNext) {
                currentString += char
                escapeNext = false
                continue
            }

            if (char === '\\') {
                escapeNext = true
                continue
            }

            if (char === '"' && !escapeNext) {
                if (inString) {
                    // Конец строки
                    if (currentString.trim()) {
                        instructions.push(currentString)
                    }
                    currentString = ''
                    inString = false
                } else {
                    // Начало строки
                    inString = true
                }
                continue
            }

            if (inString) {
                currentString += char
            }
        }

        return NextResponse.json({
            agent,
            instructions: instructions.join('\n\n')
        })

    } catch (error) {
        console.error('Error reading instructions:', error)
        return NextResponse.json({ error: 'Failed to read instructions' }, { status: 500 })
    }
}

// POST - сохранить инструкции агента
export async function POST(request: NextRequest) {
    try {
        const { agent, instructions } = await request.json()

        if (!agent || !instructions) {
            return NextResponse.json({ error: 'Agent and instructions required' }, { status: 400 })
        }

        let filePath: string
        let variableName: string
        let comment: string

        if (agent === 'consultant') {
            filePath = path.join(INSTRUCTIONS_DIR, 'consultant_instructions.py')
            variableName = 'CONSULTANT_INSTRUCTIONS'
            comment = '# Инструкции для агента-консультанта Авангард'
        } else if (agent === 'mcp') {
            filePath = path.join(INSTRUCTIONS_DIR, 'mcp_instructions.py')
            variableName = 'MCP_INSTRUCTIONS'
            comment = '# Инструкции для MCP агента управления прайс-листами'
        } else if (agent === 'warehouse') {
            filePath = path.join(INSTRUCTIONS_DIR, 'warehouse_instructions.py')
            variableName = 'WAREHOUSE_INSTRUCTIONS'
            comment = '# Инструкции для складского агента Авангард'
        } else {
            return NextResponse.json({ error: 'Invalid agent' }, { status: 400 })
        }

        console.log(`💾 Сохраняем инструкции для агента ${agent}...`)

        // Сохраняем как обычный текст без кавычек
        const fileContent = `${comment}
${variableName} = """${instructions.replace(/"""/g, '\\"""')}"""`

        await fs.writeFile(filePath, fileContent, 'utf-8')
        console.log(`✅ Инструкции сохранены в ${filePath}`)

        // Горячая перезагрузка: без очистки кеша и рестартов
        // Новые инстансы агентов подхватят свежие инструкции через importlib.reload
        return NextResponse.json({
            success: true,
            message: `Инструкции для агента ${agent} обновлены. Горячая перезагрузка активна: новые сессии используют свежие промпты.`
        })

    } catch (error) {
        console.error('Error saving instructions:', error)
        return NextResponse.json({ error: 'Failed to save instructions' }, { status: 500 })
    }
}