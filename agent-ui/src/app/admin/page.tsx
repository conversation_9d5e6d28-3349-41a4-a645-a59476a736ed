'use client'

import { Suspense, useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { TextArea } from '@/components/ui/textarea'
import { toast } from 'sonner'
import Icon from '@/components/ui/icon'

interface AgentInstructions {
  agent: string
  instructions: string
}

function AdminContent() {
  const [selectedAgent, setSelectedAgent] = useState<'consultant' | 'warehouse'>('consultant')
  const [instructions, setInstructions] = useState('')
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)
  const [rebuilding, setRebuilding] = useState(false)

  useEffect(() => {
    loadInstructions(selectedAgent)
  }, [selectedAgent])

  const loadInstructions = async (agent: 'consultant' | 'warehouse') => {
    setLoading(true)
    try {
      const response = await fetch(`/api/admin/instructions?agent=${agent}`)
      if (response.ok) {
        const data: AgentInstructions = await response.json()
        setInstructions(data.instructions || '')
      } else {
        toast.error('Ошибка загрузки инструкций')
      }
    } catch {
      toast.error('Ошибка подключения к серверу')
    } finally {
      setLoading(false)
    }
  }

  const saveInstructions = async () => {
    setSaving(true)
    try {
      const response = await fetch('/api/admin/instructions', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          agent: selectedAgent,
          instructions: instructions.trim(),
        }),
      })

      if (response.ok) {
        toast.success('Инструкции сохранены успешно!')
      } else {
        const error = await response.json()
        toast.error(`Ошибка сохранения: ${error.error || 'Неизвестная ошибка'}`)
      }
    } catch {
      toast.error('Ошибка подключения к серверу')
    } finally {
      setSaving(false)
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.ctrlKey && e.key === 's') {
      e.preventDefault()
      if (!saving && !loading) {
        saveInstructions()
      }
    }
  }

  const rebuildKnowledge = async () => {
    if (rebuilding) return
    setRebuilding(true)
    const toastId = toast.loading('Обновление базы знаний: сбор документов...')
    try {
      const response = await fetch('/api/admin/rebuild-knowledge', { method: 'POST' })
      const data = await response.json().catch(() => ({}))
      if (response.ok && data?.success) {
        toast.success('База знаний обновлена успешно!', { id: toastId })
        if (data.log) {
          // Показать последние строки лога кратко
          const lines: string[] = String(data.log).trim().split('\n')
          const tail = lines.slice(-10).join('\n')
          console.debug('[Rebuild Log]\n' + tail)
        }
      } else {
        const msg = data?.log || data?.error || 'Неизвестная ошибка'
        toast.error('Ошибка обновления базы знаний', { id: toastId, description: msg.slice(-500) })
      }
    } catch (e: any) {
      toast.error('Ошибка подключения при обновлении базы знаний', { id: toastId, description: String(e) })
    } finally {
      setRebuilding(false)
    }
  }

  return (
    <div className="min-h-screen bg-background/80 flex flex-col">
      <main className="flex-grow container mx-auto px-4 py-6 flex flex-col gap-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <h1 className="text-2xl sm:text-3xl font-bold text-primary">Админ Панель</h1>
          <p className="text-muted-foreground text-sm sm:text-base">
            Редактирование системных промптов для агентов Авангард
          </p>
        </div>

        {/* Agent Selector */}
        <div className="flex flex-wrap justify-center gap-3 sm:gap-4">
          {(['consultant', 'warehouse'] as const).map((agent) => (
            <Button
              key={agent}
              onClick={() => setSelectedAgent(agent)}
              size="lg"
              className={`h-9 min-w-36 sm:w-48 rounded-xl text-xs font-medium ${
                selectedAgent === agent
                  ? 'bg-primary text-background hover:bg-primary/80'
                  : 'bg-accent border border-primary/15 text-primary hover:bg-accent/80'
              }`}
            >
              <Icon
                type={'agent'}
                size="xs"
                className={selectedAgent === agent ? 'text-background' : 'text-primary'}
              />
              <span className="uppercase">{agent === 'consultant' ? 'Консультант' : 'Складской агент'}</span>
            </Button>
          ))}
        </div>

        {/* Section Title + Actions */}
        <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
          <h2 className="text-lg sm:text-xl font-semibold truncate">
            Инструкции для {selectedAgent === 'consultant' ? 'Консультанта' : 'Складского агента'}
          </h2>
          <div className="flex gap-2 w-full sm:w-auto">
            <Button
              onClick={rebuildKnowledge}
              disabled={rebuilding}
              size="lg"
              className="h-9 w-full sm:w-56 rounded-xl bg-accent border border-primary/15 text-primary hover:bg-accent/80 disabled:opacity-50"
            >
              <Icon type="refresh" size="xs" className="text-primary" />
              <span className="uppercase">{rebuilding ? 'Обновление…' : 'Обновить базу знаний'}</span>
            </Button>
            <Button
              onClick={saveInstructions}
              disabled={saving || loading}
              size="lg"
              className="h-9 w-full sm:w-40 rounded-xl bg-primary text-xs font-medium text-background hover:bg-primary/80 disabled:opacity-50"
            >
              <Icon type="save" size="xs" className="text-background" />
              <span className="uppercase">{saving ? 'Сохранение...' : 'Сохранить'}</span>
            </Button>
          </div>
        </div>

        {/* Editor */}
        <div className="flex-1 flex flex-col min-h-[300px] max-h-[70vh] overflow-hidden">
          {loading ? (
            <div className="flex-1 flex items-center justify-center border rounded-lg bg-muted/20">
              <p className="text-muted-foreground">Загрузка инструкций...</p>
            </div>
          ) : (
            <>
              <textarea
                value={instructions}
                onChange={(e) => setInstructions(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Введите инструкции для агента..."
                className="w-full flex-1 resize-none font-mono text-sm bg-transparent border border-border rounded-xl px-3 py-2 shadow-sm placeholder:text-muted-foreground focus-visible:ring-1 focus-visible:ring-ring focus-visible:border-primary/50 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 overflow-auto"
                disabled={saving}
                spellCheck={false}
              />
              <div className="text-xs text-muted-foreground mt-2">
                Символов: {instructions.length}
              </div>
            </>
          )}
        </div>

        {/* Footer Info */}
        <div className="bg-accent/50 rounded-lg p-3 sm:p-4 space-y-2 text-xs sm:text-sm">
          <h3 className="font-semibold">ℹ️ Информация:</h3>
          <ul className="text-muted-foreground space-y-1">
            <li>Разделяйте логические блоки пустой строкой</li>
          </ul>
        </div>
      </main>
    </div>
  )
}

export default function AdminPage() {
  return (
    <Suspense fallback={<div className="flex items-center justify-center h-screen">Загрузка...</div>}>
      <AdminContent />
    </Suspense>
  )
}
